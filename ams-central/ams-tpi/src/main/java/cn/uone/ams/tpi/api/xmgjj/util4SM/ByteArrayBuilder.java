package cn.uone.ams.tpi.api.xmgjj.util4SM;

public class ByteArrayBuilder {
    protected byte[] buf;
    protected int count;

    public ByteArrayBuilder() {
        this(32);
    }

    public ByteArrayBuilder(int size) {
        if (size < 0) {
            throw new IllegalArgumentException("Negative initial size: " + size);
        } else {
            this.buf = new byte[size];
        }
    }

    public void append(int b) {
        int newcount = this.count + 1;
        if (newcount > this.buf.length) {
            byte[] newbuf = new byte[Math.max(this.buf.length << 1, newcount)];
            System.arraycopy(this.buf, 0, newbuf, 0, this.count);
            this.buf = newbuf;
        }

        this.buf[this.count] = (byte)b;
        this.count = newcount;
    }

    public void append(byte[] b, int off, int len) {
        if (off >= 0 && off <= b.length && len >= 0 && off + len <= b.length && off + len >= 0) {
            if (len != 0) {
                int newcount = this.count + len;
                if (newcount > this.buf.length) {
                    byte[] newbuf = new byte[Math.max(this.buf.length << 1, newcount)];
                    System.arraycopy(this.buf, 0, newbuf, 0, this.count);
                    this.buf = newbuf;
                }

                System.arraycopy(b, off, this.buf, this.count, len);
                this.count = newcount;
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void append(byte[] b, int off, int len, byte def) {
        if (off >= 0 && off <= b.length && len >= 0) {
            if (len != 0) {
                if (off + len > b.length) {
                    byte[] newbuf = new byte[off + len];
                    System.arraycopy(b, 0, newbuf, 0, b.length);

                    for(int i = b.length; i < off + len - b.length; ++i) {
                        newbuf[i] = def;
                    }

                    b = newbuf;
                }

                this.append(b, off, len);
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void append(byte def, byte[] b, int off, int len) {
        if (off >= 0 && off <= b.length && len >= 0) {
            if (len != 0) {
                if (off + len > b.length) {
                    byte[] newbuf = new byte[len];

                    for(int i = 0; i < off + len - b.length; ++i) {
                        newbuf[i] = def;
                    }

                    System.arraycopy(b, off, newbuf, off + len - b.length, b.length - off);
                    b = newbuf;
                }

                this.append(b);
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void append(byte[] b) {
        this.append(b, 0, b.length);
    }

    public void insert(int pos, byte[] b, int off, int len) {
        if (pos >= 0 && pos <= this.buf.length && off >= 0 && off <= b.length && len >= 0 && off + len <= b.length && off + len >= 0) {
            if (len != 0) {
                byte[] buffer = new byte[this.buf.length + len];
                boolean flag = false;
                int i = 0;
                int j = 0;

                for(int k = 0; k < buffer.length; ++k) {
                    if (k == pos) {
                        buffer[k] = b[j + off];
                        flag = true;
                        ++j;
                    } else if (flag) {
                        if (j < len) {
                            buffer[k] = b[j + off];
                            ++j;
                        } else {
                            buffer[k] = this.buf[i];
                            ++i;
                        }
                    } else {
                        buffer[k] = this.buf[i];
                        ++i;
                    }
                }

                this.buf = buffer;
                this.count += len;
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void insert(int pos, byte[] b, int off, int len, byte def) {
        if (pos >= 0 && pos < this.buf.length && off >= 0 && off <= b.length && len >= 0 && off + len >= 0) {
            if (len != 0) {
                if (off + len > b.length) {
                    byte[] newbuf = new byte[off + len];
                    System.arraycopy(b, 0, newbuf, 0, b.length);

                    for(int i = b.length; i < off + len - b.length; ++i) {
                        newbuf[i] = def;
                    }

                    b = newbuf;
                }

                this.insert(pos, b, off, len);
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void insert(byte def, int pos, byte[] b, int off, int len) {
        if (pos >= 0 && pos < this.buf.length && off >= 0 && off <= b.length && len >= 0 && off + len >= 0) {
            if (len != 0) {
                if (off + len > b.length) {
                    byte[] newbuf = new byte[len];

                    for(int i = 0; i < off + len - b.length; ++i) {
                        newbuf[i] = def;
                    }

                    System.arraycopy(b, off, newbuf, off + len - b.length, b.length - off);
                    b = newbuf;
                }

                this.insert(pos, b);
            }
        } else {
            throw new IndexOutOfBoundsException();
        }
    }

    public void insert(int pos, byte[] b) {
        this.insert(pos, b, 0, b.length);
    }

    public void reset() {
        this.count = 0;
    }

    public byte[] toByteArray() {
        byte[] newbuf = new byte[this.count];
        System.arraycopy(this.buf, 0, newbuf, 0, this.count);
        return newbuf;
    }

    public byte[] toArray(byte[] b) {
        byte[] newbuf = b;
        if (b == null || b.length < this.count) {
            newbuf = new byte[this.count];
        }

        System.arraycopy(this.buf, 0, newbuf, 0, this.count);
        return newbuf;
    }

    public int size() {
        return this.count;
    }

    public String toString() {
        return new String(this.buf, 0, this.count);
    }
}
