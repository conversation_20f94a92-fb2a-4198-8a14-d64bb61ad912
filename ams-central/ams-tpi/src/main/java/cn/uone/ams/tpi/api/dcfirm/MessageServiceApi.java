package cn.uone.ams.tpi.api.dcfirm;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.XML;
import cn.uone.web.base.RestResponse;
import netbank.firm.service.MessageService;
import netbank.firm.util.CibProfile;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 兴业银行-银企直连
 */
@Component
public class MessageServiceApi {

    /**
     * 账户查询-本行账户余额和交易流水分页查询
     * @param config  接口配置
     * @param beginDate  起始日期，8位格式：yyyyMMdd（起始日期必须小于等于截止日期）
     * @param endDate 截止日期，8位格式：yyyyMMdd（起始日期必须小于等于截止日期）
     * @return
     */
    public RestResponse iBacctDetailQueryTrnrq(JSONObject config,String beginDate,String endDate){
        String dtClient = DateUtil.formatDateTime(new Date()); //请求时间
        String cid = config.get("cid").toString();//客户号
        String userId = config.get("userId").toString();//操作员
        String userPass = config.get("userPass").toString();//登录密码
        String trnuId = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");//客户端交易的唯一流水号建议值为YYYYMMDD+序号
        String acctId = config.get("acctId").toString();//查询账号，18位
        int page = 1;
        //请求报文
        String postData = "<FOX><SIGNONMSGSRQV1><SONRQ><DTCLIENT>{}</DTCLIENT><CID>{}</CID><USERID>{}</USERID><USERPASS>{}</USERPASS></SONRQ></SIGNONMSGSRQV1><SECURITIES_MSGSRQV1><IBACCTDETAILQUERYTRNRQ><TRNUID>{}</TRNUID><RQBODY PAGE=\"{}\" SIZE=\"100\"><ACCT_ID>{}</ACCT_ID><BEGIN_DATE>{}</BEGIN_DATE><END_DATE>{}</END_DATE><TRANS_TYPE>1</TRANS_TYPE><SORT_ORDER>0</SORT_ORDER></RQBODY></IBACCTDETAILQUERYTRNRQ></SECURITIES_MSGSRQV1></FOX>";
        postData = StrUtil.format(postData,dtClient,cid,userId,userPass,trnuId,page,acctId,beginDate,endDate);

        String serviceUrl = config.get("serviceUrl").toString();
        String certPassword = config.get("certPassword").toString();
        String certPath = config.get("certPath").toString();
        String sslCertPath = config.get("sslCertPath").toString();
        RestResponse res = null;
        try {
            res = clientLessSendMessageNetty(serviceUrl,cid,certPassword,certPath,sslCertPath,postData);
            if(res.getSuccess()){
                JSONArray array = (JSONArray) res.get("CONTENT");
                int total = (int) res.get("total");
                int totalPages = (total + 100 - 1) / 100;
                for (int i = 2; i < totalPages +1; i++) {
                    postData = StrUtil.format(postData,dtClient,cid,userId,userPass,trnuId,i,acctId,beginDate,endDate);
                    res = clientLessSendMessageNetty(serviceUrl,cid,certPassword,certPath,sslCertPath,postData);
                    if(res.getSuccess()){
                        JSONArray array2 = (JSONArray) res.get("CONTENT");
                        array.addAll(array2);
                    }
                }
                res.setData(array);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return res;
    }

    /**
     * 报文请求
     * @param serviceUrl 接口地址
     * @param ncid   客户号
     * @param certPassword 证书密码
     * @param certFilePath  证书地址
     * @param sslCertFilePath  ssl证书地址
     * @param postData  请求xml报文
     * @return
     * @throws Exception
     */
    private RestResponse clientLessSendMessageNetty(String serviceUrl,
                                                    String ncid, String certPassword,
                                                    String certFilePath, String sslCertFilePath,
                                                    String postData) throws Exception {
        CibProfile profile = new CibProfile();
        profile.setNcid(ncid);
        profile.setCertFilePath(certFilePath);
        profile.setCertPassword(certPassword);
        //“sm2ca_beta.cer”为测试环境的ssl证书，生产需改为“sm2ca.cer”
        profile.setSslCertFilePath(sslCertFilePath);
        profile.setServiceUrl(serviceUrl);
        //该对sm2公私钥为测试环境的，生产需改为X:“F5C9161512EA12187A45414F47D7360389241E179BF5ED8E7B527AFDF9E5E384”
        // 和Y:"46D66DA4E50B42CFBD6A65CCF6192E8D8CF8C15E3CC9EBCBF33DEE94FDDE30DB"
        //profile.setPublicKeyX("2D08EE0028C2CC24E7A83C81AC8C111D3616A6B5B4A9572B1B921C14FEAA37BD");
        //profile.setPublicKeyY("D4C9B15C846ECBE499F523C8CCFF46392385B5BCC616B35BB766B97743B0B957");
        Console.log("CibProfile:{}", JSONUtil.toJsonStr(profile));
        MessageService messageService = new MessageServiceHandler(profile);
        String xmlStr = messageService.postDataNoMBFEByNetty(postData);
        // 将XML转换为JSON对象
        JSONObject json = XML.toJSONObject(xmlStr);
        Console.log("resJson:{}", JSONUtil.toJsonStr(json));
        // 格式化输出结果
        //String jsonStr = json.toStringPretty();
        JSONObject FOX = json.getJSONObject("FOX");
        JSONObject SIGNONMSGSRSV1 = FOX.getJSONObject("SIGNONMSGSRSV1");
        JSONObject SONRS = SIGNONMSGSRSV1.getJSONObject("SONRS");
        JSONObject STATUS1 = SONRS.getJSONObject("STATUS");
        int CODE1 = STATUS1.getInt("CODE");
        if(CODE1 != 0){
            String MESSAGE1 = STATUS1.getStr("MESSAGE");
            return RestResponse.failure(MESSAGE1);
        }
        JSONObject SECURITIES_MSGSRSV1 = FOX.getJSONObject("SECURITIES_MSGSRSV1");
        JSONObject IBACCTDETAILQUERYTRNRS = SECURITIES_MSGSRSV1.getJSONObject("IBACCTDETAILQUERYTRNRS");
        JSONObject STATUS2 = IBACCTDETAILQUERYTRNRS.getJSONObject("STATUS");
        int CODE2 = STATUS2.getInt("CODE");
        if(CODE2 != 0){
            String MESSAGE2 = STATUS1.getStr("MESSAGE");
            return RestResponse.failure(MESSAGE2);
        }
        JSONObject RSBODY = IBACCTDETAILQUERYTRNRS.getJSONObject("RSBODY");
        int TOTAL_NUM = RSBODY.getInt("TOTAL_NUM");//查询返回的总笔数
        JSONArray CONTENT = new JSONArray();//账户往来信息明细
        Object rootContent = RSBODY.get("CONTENT");
        // 判断类型
        if (rootContent instanceof JSONObject) {
            CONTENT.add(rootContent);
        } else if (rootContent instanceof JSONArray) {
            CONTENT = JSONUtil.parseArray(rootContent);
        }
        return RestResponse.success().setData(CONTENT).setAny("total", TOTAL_NUM);
    }
}
