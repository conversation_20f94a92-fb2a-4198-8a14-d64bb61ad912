package cn.uone.ams.tpi.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.api.gzxyDoorLock.GzxyDoorLockApi;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.bus.IResProjectFegin;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.tpi.IGzxyDoorLockFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Api(value = "广州翔羽门锁接口", tags = {"设备接口"})
@RestController
@RequestMapping("/gzxyDoorLock")
public class GzxyDoorLockController extends BaseController implements IGzxyDoorLockFegin {

    @Autowired
    IExpenseConfigFegin expenseConfigFegin;
    @Autowired
    GzxyDoorLockApi gzxyDoorLockApi;
    @Autowired
    IResProjectFegin resProjectFegin;
    @Autowired
    ISysCompanyFegin sysCompanyFegin;

    /**
     * 获取接口参数配置
     * @param projectId  房源项目Id
     * @return
     */
    private JSONObject getConfig(@RequestParam String projectId){
        ResProjectEntity project = resProjectFegin.getById(projectId);
        SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());
        String configProjectId = company.getTopId();
        JSONObject config = JSONUtil.parseObj(CacheUtil.get("GZXYDOORLOCK_"+configProjectId));
        if(config.isEmpty()){
            config = expenseConfigFegin.getExpenseConfigByPro(configProjectId, ApiTypeEnum.GZXYDOORLOCK.getValue());
            CacheUtil.putEx("GZXYDOORLOCK_"+configProjectId,config,1l, TimeUnit.DAYS);
        }
        return config;
    }

    /**
     * 生成一次性密码
     * @param projectId
     * @param roomId
     * @return
     */
    @PostMapping("/apartmentAddSingleKey")
    @ApiOperation("生成一次性密码")
    public RestResponse apartmentAddSingleKey(@RequestParam("projectId") String projectId, @RequestParam("roomId")String roomId){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentAddSingleKey(accountName,password,roomId);
    }

    /**
     * 远程下发密码授权
     * @param projectId
     * @param roomId
     * @param phoneNo
     * @param beginTime
     * @param endTime
     * @return
     */
    @PostMapping("/apartmentAddPasswordKey")
    @ApiOperation("远程下发密码授权")
    public RestResponse apartmentAddPasswordKey(@RequestParam("projectId") String projectId, @RequestParam("roomId")String roomId,
                                                @RequestParam("phoneNo")String phoneNo,@RequestParam("roomPassword")String roomPassword,
                                                @RequestParam("beginTime")Date beginTime, @RequestParam("endTime")Date endTime){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentAddPasswordKey(accountName,password,roomId,phoneNo,beginTime,endTime,roomPassword);
    }

    /**
     * 冻结用户
     * @param projectId
     * @param roomId
     * @param phoneNo
     * @return
     */
    @PostMapping("/apartmentRoomFrozenTenant")
    @ApiOperation("冻结用户")
    public RestResponse apartmentRoomFrozenTenant(@RequestParam("projectId") String projectId,  @RequestParam("roomId")String roomId,
                                                  @RequestParam("phoneNo") String phoneNo){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentRoomFrozenTenant(accountName,password,roomId,phoneNo);
    }

    /**
     * 解冻用户
     * @param projectId
     * @param roomId
     * @param phoneNo
     * @return
     */
    @PostMapping("/apartmentRoomUnFrozenTenant")
    @ApiOperation("解冻用户")
    public RestResponse apartmentRoomUnFrozenTenant(@RequestParam("projectId") String projectId, @RequestParam("roomId") String roomId,
                                                    @RequestParam("phoneNo") String phoneNo){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentRoomUnFrozenTenant(accountName,password,roomId,phoneNo);
    }

    /**
     * 房间退租
     * @param projectId
     * @param roomId
     * @return
     */
    @PostMapping("/apartmentRoomCheckOut")
    @ApiOperation("房间退租")
    public RestResponse apartmentRoomCheckOut(@RequestParam("projectId") String projectId, @RequestParam("roomId")String roomId){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentRoomCheckOut(accountName,password,roomId);
    }

    /**
     * 修改密码
     * @param projectId
     * @param keyId
     * @return
     */
    @PostMapping("/apartmentModifyKey")
    @ApiOperation("修改密码")
    public RestResponse apartmentModifyKey(@RequestParam("projectId") String projectId, @RequestParam("keyId")String keyId,
                                           @RequestParam("roomPassword") String roomPassword){
        JSONObject config = getConfig(projectId);
        String accountName = config.getStr("accountName");
        String password = config.getStr("password");
        return gzxyDoorLockApi.apartmentModifyKey(accountName,password,keyId,roomPassword);
    }


}
