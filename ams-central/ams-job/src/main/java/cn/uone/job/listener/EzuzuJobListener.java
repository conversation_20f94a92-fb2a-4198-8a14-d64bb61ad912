package cn.uone.job.listener;

import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.job.entity.QrtzJobHistory;
import cn.uone.web.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

@Slf4j
public class EzuzuJobListener implements JobListener {
    protected final static Logger logger = LoggerFactory.getLogger(EzuzuJobListener.class);

    ISysMsgTemplateFegin smsFegin;

    String[] smsClass={"cn.uone.job.task.BatchContAccountJob",
            "cn.uone.job.task.BatchRentAccountJob"};

    public EzuzuJobListener(ISysMsgTemplateFegin smsFegin){
        this.smsFegin = smsFegin;
    }

    @Override
    public String getName() {
        return "EzuzuJobListener";
    }

    //Scheduler 在 JobDetail 将要被执行时调用这个方法。
    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        log.info("EzuzuJobListener :{}将被执行. ",context.getJobDetail().getKey().getName());
    }

    //Scheduler 在 JobDetail 即将被执行，但又被 TriggerListener 否决了时调用这个方法。
    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
        log.info("EzuzuJobListener : {} 被否决  ",context.getJobDetail().getKey().getName());
    }

    //Scheduler 在 JobDetail 被执行之后调用这个方法。
    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        log.info("EzuzuJobListener : {} 被执行  ",context.getJobDetail().getKey().getName());
        StringBuilder params = new StringBuilder();
        JobDataMap paramMap = context.getJobDetail().getJobDataMap();
        for (Entry<String, Object> entry : paramMap.entrySet()) {
            if (params.length() == 0) {
                params = params.append(entry.getKey()).append("=").append(entry.getValue());
            } else {
                params = params.append("&").append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        QrtzJobHistory history = new QrtzJobHistory();

        Date now = new Date();
        history.setStartTime(new java.sql.Timestamp(now.getTime() - context.getJobRunTime()));

        Date startTime = new Date(now.getTime() - context.getJobRunTime());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        history.setJobName(context.getJobDetail().getKey().getName());
        history.setParams(params.toString());
        history.setJobClass(context.getJobDetail().getJobClass().getName());
        history.setEndTime(new java.sql.Timestamp(now.getTime()));
        if (null != jobException) {
            history.setResult(0);
            history.setRemarks(context.getJobDetail().getKey().getName() + " 发生异常;" + jobException.getLocalizedMessage());
            String profile = SpringUtil.getApplicationContext().getEnvironment().getActiveProfiles()[0];
            /*if(Arrays.asList(smsClass).contains(context.getJobDetail().getJobClass().getName())&&"PROD".equals(profile.toUpperCase())){
                Map<String, Object> sms = new HashMap<String, Object>();
                sms.put("mobile", "18064483525");
                sms.put("code", context.getJobDetail().getKey().getName()); // 模板变量
                sms.put("template_code", "97211"); // 模板code
                try {
                    smsFegin.send(sms);
                } catch (Exception e) {
                    logger.error("发送短信失败，"+e.getMessage());
                }
            }*/
        } else {
            history.setResult(1);
            history.setRemarks(context.getJobDetail().getKey().getName() + ":Success");
        }

        history.insert();
    }

}
