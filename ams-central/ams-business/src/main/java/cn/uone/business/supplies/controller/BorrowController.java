package cn.uone.business.supplies.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.supplies.BorrowEntity;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.business.supplies.service.IBorrowService;
import cn.uone.business.supplies.service.ICategoryService;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 领用申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@RestController
@RequestMapping("/supplies/borrow")
public class BorrowController extends BaseController {

    @Autowired
    private IBorrowService service;
    @Autowired
    private ICategoryService categoryService;



    @GetMapping("/page")
    public RestResponse page(Page<BorrowEntity> page, BorrowEntity entity){
        QueryWrapper<BorrowEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("apply_time");
//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getApplicant())){
            wrapper.like("applicant","%"+entity.getApplicant()+"%");
        }
        if(StrUtil.isNotBlank(entity.getItemName())){
            wrapper.like("item_name","%"+entity.getItemName()+"%");
        }
        IPage<BorrowEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

//    @GetMapping("/listBySourceId")
//    public RestResponse listBySourceId(String sourceId){
//        QueryWrapper<ResProjectCompanyEntity> wrapper = new QueryWrapper<>();
//        wrapper.exists("select 1 from t_res_source s where s.project_id = t_res_project_company.project_id and s.id = '"+sourceId+"'");
//        List<ResProjectCompanyEntity> list =  service.list(wrapper);
//        list.forEach(item->{
//            item.setValue(item.getId());
//        });
//        return RestResponse.success().setData(list);
//    }

    @PostMapping("/save")
    public RestResponse save(BorrowEntity entity){
        CategoryEntity categoryEntity= categoryService.getById(entity.getMaterialCategoryId());
        entity.setItemName(categoryEntity.getName());
        entity.setState("1");
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        //修改库存表数量
        categoryEntity.setNowQuantity(categoryEntity.getNowQuantity()-entity.getQuantity());
        categoryEntity.setLendQuantity(categoryEntity.getLendQuantity()+entity.getQuantity());
        categoryEntity.updateById();
//        //生成出库表
//        CheckoutEntity checkoutEntity = new CheckoutEntity();
//        checkoutEntity.setOperator(UoneSysUser.nickName());
//        checkoutEntity.setOperatorId(UoneSysUser.id());
//        checkoutEntity.setItemName(entity.getItemName());
//        checkoutEntity.setQuantity(entity.getQuantity());
//        checkoutEntity.setMaterialCategoryId(entity.getMaterialCategoryId());
//        checkoutEntity.setCheckoutTime(new Date());
//        checkoutEntity.setType("2");
//        checkoutEntity.setState("1");
//        checkoutEntity.setRemark("借用");
//        boolean insert =  checkoutEntity.insertOrUpdate();
//        if(insert){
//            entity.insertOrUpdate();
//            //修改库存表数量
//            categoryEntity.setNowQuantity(categoryEntity.getNowQuantity()-entity.getQuantity());
//            categoryEntity.setLendQuantity(categoryEntity.getLendQuantity()+entity.getQuantity());
//            categoryEntity.updateById();
//        }else {
//            return RestResponse.failure("操作失败");
//        }
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(BorrowEntity entity){
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @PostMapping("/giveBack")
    public RestResponse giveBack(BorrowEntity borrowEntity){
        borrowEntity.setState("2");
        borrowEntity.setReturnDate(new Date());
        borrowEntity.setTransactor(UoneSysUser.nickName());
        borrowEntity.setTransactorId(UoneSysUser.id());
        CategoryEntity categoryEntity= categoryService.getById(borrowEntity.getMaterialCategoryId());
        //修改库存表数量
        categoryEntity.setNowQuantity(categoryEntity.getNowQuantity()+borrowEntity.getQuantity());
        categoryEntity.setLendQuantity(categoryEntity.getLendQuantity()-borrowEntity.getQuantity());
        boolean insert =  categoryEntity.insertOrUpdate();
        if(insert){
            borrowEntity.insertOrUpdate();
        }else{
            return RestResponse.failure("操作失败");
        }
        return RestResponse.success();
    }
    @PostMapping("/fail")
    public RestResponse fail(BorrowEntity entity){
        entity.setState("3");
        entity.setAuditTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
//

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

//    @RequestMapping("/getCompanyList")
//    public RestResponse getByProjectId() {
//        QueryWrapper query = new QueryWrapper<>();
//        //query.eq("project_id", UoneHeaderUtil.getProjectId());
//        return RestResponse.success().setData(service.list(query));
//    }
//
//    /*
//       下拉菜单获取公司名（name）及id(value)
//     */
//    @RequestMapping("/selectCompany")
//    public RestResponse selectCompany() {
//        RestResponse response=new RestResponse();
//        List<ResProjectCompanyEntity> companyEntities=service.list();
//        List<Map<String,String>> companyList=new ArrayList<>();
//        for(ResProjectCompanyEntity companyEntity:companyEntities){
//            Map<String,String> map=new HashMap<>();
//            map.put("name",companyEntity.getName());
//            map.put("value",companyEntity.getId());
//            companyList.add(map);
//        }
//        return response.setSuccess(true).setData(companyList);
//    }
//
//    @RequestMapping("/getEasBank")
//    @UonePermissions
//    public RestResponse getEasBank(@RequestParam("city") String city,@RequestParam(value="bankName",required = false) String bankName) {
//        JSONArray arr = new JSONArray();
//        if(StrUtil.isBlank(city)){
//            return RestResponse.success().setData(arr);
//        }
//        Map<String,Object> paras = Maps.newHashMap();
//        paras.put("city",city);
//        paras.put("bankName",bankName);
//        String json = HttpUtil.post(easUrl+"/getEasBank",paras);
//        JSONObject obj = JSONUtil.parseObj(json);
//        arr = obj.getJSONArray("data");
//        return RestResponse.success().setData(arr);
//    }
//
//    @RequestMapping("/getCompanyBank")
//    @UonePermissions
//    public RestResponse getCompanyBank(@RequestParam("id") String id) {
//        JSONArray arr = new JSONArray();
//        if(StrUtil.isBlank(id)){
//            return RestResponse.success().setData(arr);
//        }
//        Map<String,Object> paras = Maps.newHashMap();
//        paras.put("company",id);
//        String json = HttpUtil.post(easUrl+"/getCompanyBank",paras);
//        JSONObject obj = JSONUtil.parseObj(json);
//        arr = obj.getJSONArray("data");
//        return RestResponse.success().setData(arr);
//    }

}
