package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.source.DetailInventoryStateEnum;
import cn.uone.application.enumerate.source.HandleStatusEnum;
import cn.uone.application.enumerate.source.InventoryStateEnum;
import cn.uone.application.enumerate.source.InventoryTypeEnum;
import cn.uone.bean.entity.business.fixed.*;
import cn.uone.bean.entity.business.fixed.vo.AssetInventoryVo;
import cn.uone.business.fixed.dao.AssetInventoryDao;
import cn.uone.business.fixed.service.*;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 固定资产盘点主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
public class AssetInventoryServiceImpl extends ServiceImpl<AssetInventoryDao, AssetInventoryEntity> implements IAssetInventoryService {

    @Autowired
    AssetInventoryDao assetInventoryDao;

    @Resource
    private IAssetCategoryService iAssetCategoryService;

    @Resource
    private IAssetLocationService iAssetLocationService;

    @Resource
    private IAssetFromService iAssetFromService;

    @Resource
    private IAssetDiscardedService iAssetDiscardedService;

    @Resource
    private IFixedPropertyService iFixedPropertyService;

    @Resource
    private IAssetInventoryDetailService iAssetInventoryDetailService;

    @Override
    public IPage<AssetInventoryEntity> page(Page page, AssetInventoryEntity entity) {
        QueryWrapper<AssetInventoryEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getInventoryCode())) {
            queryWrapper.like("t_fixed_asset_inventory.inventory_code", entity.getInventoryCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getInventoryName())) {
            queryWrapper.like("t_fixed_asset_inventory.inventory_name", entity.getInventoryName());
        }
        queryWrapper.orderByDesc("t_fixed_asset_inventory.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);
        return iPage;
    }

    @Override
    public IPage<AssetInventoryVo> pageList(Page page, Map<String, Object> map) {
        DataScope scope=new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        return baseMapper.pageList(page,scope,map);
    }

    /**
     *
     * @param page 分页
     * @param keyword 关键字
     * @param flag  旗帜标识
     * @return
     */
    @Override
    public List<HashMap<String,String>> keywordList(Page page, String keyword, String flag) {
        if(ObjectUtil.isEmpty(flag)){flag="rfidList";}
        List<HashMap<String,String>> list = new ArrayList<>();
        if("propertyType".equals(flag)){//资产分类
            AssetCategoryEntity assetCategory = new AssetCategoryEntity();
            assetCategory.setCategoryName(keyword);
            List<AssetCategoryEntity> assetCategorylist = iAssetCategoryService.selectList(assetCategory);
            for(AssetCategoryEntity entity : assetCategorylist){
                HashMap<String,String> map = new HashMap<>();
                map.put("value",entity.getId());
                map.put("name",entity.getCategoryName());
                list.add(map);
            }
        }else if("itemName".equals(flag)){//资产
            FixedPropertyEntity fixedProperty = new FixedPropertyEntity();
            fixedProperty.setPropertyName(keyword);
            IPage<FixedPropertyEntity> fixedPropertyList = iFixedPropertyService.page(page,fixedProperty);
            for(FixedPropertyEntity entity : fixedPropertyList.getRecords()){
                HashMap<String,String> map = new HashMap<>();
                map.put("id",entity.getId());
                map.put("name",entity.getPropertyName());
                map.put("code",entity.getPropertyCode());
                list.add(map);
            }
        }else if("address".equals(flag)){//存放位置/资产存放地址
            AssetLocationEntity entity = new AssetLocationEntity();
            entity.setLocationName(keyword);
            List<AssetLocationEntity> listI = iAssetLocationService.selectList(entity);
            for(AssetLocationEntity entity2 : listI){
                HashMap<String,String> map = new HashMap<>();
                map.put("value",entity2.getId());
                map.put("name",entity2.getLocationName());
                list.add(map);
            }
        }else if("sourceType".equals(flag)){//资产来源
            AssetFromEntity assetFromEntity = new AssetFromEntity();
            assetFromEntity.setFromName(keyword);
            List<AssetFromEntity> assetList = iAssetFromService.selectList(assetFromEntity);
            for(AssetFromEntity entity : assetList){
                HashMap<String,String> map = new HashMap<>();
                map.put("value",entity.getId());
                map.put("name",entity.getFromName());
                list.add(map);
            }
        }else if("discarded".equals(flag)){//报废原因
            AssetDiscardedEntity entity = new AssetDiscardedEntity();
            entity.setDiscardedReason(keyword);
            List<AssetDiscardedEntity> assetList = iAssetDiscardedService.selectList(entity);
            for(AssetDiscardedEntity entity2 : assetList){
                HashMap<String,String> map = new HashMap<>();
                map.put("value",entity2.getId());
                map.put("name",entity2.getDiscardedReason());
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 新增盘点任务
     * @param assetInventoryVo
     * @return
     */
    @Override
    @Transactional
    public boolean saveInventory(AssetInventoryVo assetInventoryVo){
        boolean result = true;
        String projectId = UoneHeaderUtil.getProjectId();
        String inventoryBy = assetInventoryVo.getInventoryBy();
        String propertyName = assetInventoryVo.getPropertyNameAdd();
        String propertyTypeIds = assetInventoryVo.getPropertyTypeIdsAdd();
        String deptIds = assetInventoryVo.getDeptIdsAdd();
        String assetLocationIds = assetInventoryVo.getAssetLocationIdsAdd();
        String memberNames = assetInventoryVo.getMemberNamesAdd();
        String assetStateNames = assetInventoryVo.getAssetStateNamesAdd();
        Date purchaseDateStart = assetInventoryVo.getPurchaseDateStartAdd();
        Date purchaseDateEnd = assetInventoryVo.getPurchaseDateEndAdd();
        AssetInventoryEntity inventory = new AssetInventoryEntity();
        inventory.setProjectId(projectId);
        inventory.setInventoryCode(assetInventoryVo.getInventoryCode());
        inventory.setInventoryName(assetInventoryVo.getInventoryName());
        inventory.setInventoryBy(inventoryBy);
        inventory.setRequiredCompleteTime(assetInventoryVo.getRequiredCompleteTime());
        inventory.setRemark(assetInventoryVo.getRemark());
        inventory.setInventoryState(InventoryStateEnum.UNINVENTORY.getValue());//默认未盘点
        inventory.setHandleStatus(HandleStatusEnum.NORMAL.getValue());
        inventory.setInventoryType(InventoryTypeEnum.ORDINARY.getValue());
        inventory.setQueryPropertyName(propertyName);
        inventory.setQueryPropertyTypeIds(propertyTypeIds);
        inventory.setQueryDeptIds(deptIds);
        inventory.setQueryAssetLocationIds(assetLocationIds);
        inventory.setQueryMemberNames(memberNames);
        inventory.setQueryAssetStateNames(assetStateNames);
        inventory.setQueryPurchaseDateStart(purchaseDateStart);
        inventory.setQueryPurchaseDateEnd(purchaseDateEnd);
        Map<String,Object> map = Maps.newHashMap();
        map.put("propertyName",propertyName);
        if(propertyTypeIds != null && !propertyTypeIds.equals("")){
            String[] propertyTypeIdsList = propertyTypeIds.split(",");
            map.put("propertyTypeIdsList",propertyTypeIdsList);
        }
        if(deptIds != null && !deptIds.equals("")){
            String[] deptIdsList = deptIds.split(",");
            map.put("deptIdsList",deptIdsList);
        }
        if(assetLocationIds != null && !assetLocationIds.equals("")){
            String[] assetLocationIdsList = assetLocationIds.split(",");
            map.put("assetLocationIdsList",assetLocationIdsList);
        }
        if(memberNames != null && !memberNames.equals("")){
            String[] memberNamesList = memberNames.split(",");
            map.put("memberNamesList",memberNamesList);
        }
        if(assetStateNames != null && !assetStateNames.equals("")){
            String[] assetStateNamesList = assetStateNames.split(",");
            map.put("assetStateNamesList",assetStateNamesList);
        }
        map.put("purchaseDateStart",purchaseDateStart);
        map.put("purchaseDateEnd",purchaseDateEnd);
        List<FixedPropertyEntity> list = iFixedPropertyService.getPropertyList(map);//根据盘点条件获取需要盘点的资产列表
        inventory.setInventoryTotal(list.size());//设置盘点总数量
        inventory.setInventoryChecked(0);//设置已盘点数量，初始化默认为0
        inventory.setInventoryUnchecked(list.size());//设置未盘点数量,初始化默认为所有资产数量
        boolean result1 = this.save(inventory);//保存盘点任务
        if(!result1){
            result = false;
        }
        String inventoryId = inventory.getId();
        for(FixedPropertyEntity property:list){
           AssetInventoryDetailEntity detail = new AssetInventoryDetailEntity();
           String propertyId = property.getId();
           detail.setInventoryId(inventoryId);
           detail.setProjectId(projectId);
           detail.setPropertyId(propertyId);
           detail.setInventoryBy(inventoryBy);
           detail.setInventoryState(InventoryStateEnum.UNINVENTORY.getValue());
           detail.setHandleStatus(HandleStatusEnum.NORMAL.getValue());
           boolean result2 = iAssetInventoryDetailService.save(detail);
           if(!result2){
               result = false;
           }
        }
        return result;
    }

    /**
     * 修改盘点任务
     * @param assetInventoryVo
     * @return
     */
    @Override
    @Transactional
    public boolean updateInventory(AssetInventoryVo assetInventoryVo){
        boolean result = true;
        String inventoryId = assetInventoryVo.getId();
        String projectId = UoneHeaderUtil.getProjectId();
        String inventoryBy = assetInventoryVo.getInventoryBy();
        String propertyName = assetInventoryVo.getPropertyNameAdd();
        String propertyTypeIds = assetInventoryVo.getPropertyTypeIdsAdd();
        String deptIds = assetInventoryVo.getDeptIdsAdd();
        String assetLocationIds = assetInventoryVo.getAssetLocationIdsAdd();
        String memberNames = assetInventoryVo.getMemberNamesAdd();
        String assetStateNames = assetInventoryVo.getAssetStateNamesAdd();
        Date purchaseDateStart = assetInventoryVo.getPurchaseDateStartAdd();
        Date purchaseDateEnd = assetInventoryVo.getPurchaseDateEndAdd();
        AssetInventoryEntity inventory = this.getById(inventoryId);
        inventory.setInventoryCode(assetInventoryVo.getInventoryCode());
        inventory.setInventoryName(assetInventoryVo.getInventoryName());
        inventory.setInventoryBy(inventoryBy);
        inventory.setRequiredCompleteTime(assetInventoryVo.getRequiredCompleteTime());
        inventory.setRemark(assetInventoryVo.getRemark());
        inventory.setInventoryState(InventoryStateEnum.UNINVENTORY.getValue());//默认未盘点
        inventory.setHandleStatus(HandleStatusEnum.NORMAL.getValue());//默认处理状态未出现异常
        inventory.setInventoryType(InventoryTypeEnum.ORDINARY.getValue());//默认盘点类型为普通盘点
        inventory.setQueryPropertyName(propertyName);
        inventory.setQueryPropertyTypeIds(propertyTypeIds);
        inventory.setQueryDeptIds(deptIds);
        inventory.setQueryAssetLocationIds(assetLocationIds);
        inventory.setQueryMemberNames(memberNames);
        inventory.setQueryAssetStateNames(assetStateNames);
        inventory.setQueryPurchaseDateStart(purchaseDateStart);
        inventory.setQueryPurchaseDateEnd(purchaseDateEnd);
        Map<String,Object> map = Maps.newHashMap();
        map.put("propertyName",propertyName);
        if(propertyTypeIds != null && !propertyTypeIds.equals("")){
            String[] propertyTypeIdsList = propertyTypeIds.split(",");
            map.put("propertyTypeIdsList",propertyTypeIdsList);
        }
        if(deptIds != null && !deptIds.equals("")){
            String[] deptIdsList = deptIds.split(",");
            map.put("deptIdsList",deptIdsList);
        }
        if(assetLocationIds != null && !assetLocationIds.equals("")){
            String[] assetLocationIdsList = assetLocationIds.split(",");
            map.put("assetLocationIdsList",assetLocationIdsList);
        }
        if(memberNames != null && !memberNames.equals("")){
            String[] memberNamesList = memberNames.split(",");
            map.put("memberNamesList",memberNamesList);
        }
        if(assetStateNames != null && !assetStateNames.equals("")){
            String[] assetStateNamesList = assetStateNames.split(",");
            map.put("assetStateNamesList",assetStateNamesList);
        }
        map.put("purchaseDateStart",purchaseDateStart);
        map.put("purchaseDateEnd",purchaseDateEnd);
        boolean delDetailRes = iAssetInventoryDetailService.deleteDetailByInventoryId(inventoryId);//先删除旧的盘点任务详情记录
        List<FixedPropertyEntity> list = iFixedPropertyService.getPropertyList(map);//查出资料列表重新添加
        inventory.setInventoryTotal(list.size());//设置盘点总数量
        inventory.setInventoryChecked(0);//设置已盘点数量，初始化默认为0
        inventory.setInventoryUnchecked(list.size());//设置未盘点数量,初始化默认为所有资产数量
        boolean result1 = this.updateById(inventory);//修改盘点任务
        if(!result1){
            result = false;
        }
        for(FixedPropertyEntity property:list){
            AssetInventoryDetailEntity detail = new AssetInventoryDetailEntity();
            String propertyId = property.getId();
            detail.setInventoryId(inventoryId);
            detail.setProjectId(projectId);
            detail.setPropertyId(propertyId);
            detail.setInventoryBy(inventoryBy);
            detail.setInventoryState(DetailInventoryStateEnum.UNINVENTORY.getValue());
            detail.setHandleStatus(HandleStatusEnum.NORMAL.getValue());
            boolean result2 = iAssetInventoryDetailService.save(detail);
            if(!result2){
                result = false;
            }
        }
        return result;
    }

    @Override
    public AssetInventoryVo getInventoryById(String inventoryId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("inventoryId",inventoryId);
        return baseMapper.getInventoryByMap(map);
    }

    /**
     * 更新盘点任务为部分盘点
     * @param inventoryId
     * @return
     */
    @Override
    @Transactional
    public boolean updateAssetInventory(String inventoryId) {
        boolean ressult = true;
        AssetInventoryEntity assetInventory = this.getById(inventoryId);
        int inventoryTotal = assetInventory.getInventoryTotal();
        int inventoryChecked = assetInventory.getInventoryChecked() + 1;//已盘点数量
        int inventoryUnchecked = inventoryTotal - inventoryChecked;//未盘点数量
        assetInventory.setInventoryChecked(inventoryChecked);
        assetInventory.setInventoryUnchecked(inventoryUnchecked);
        assetInventory.setInventoryState(InventoryStateEnum.PARTINVENTORY.getValue());
        ressult = this.saveOrUpdate(assetInventory);
        return ressult;
    }
}
