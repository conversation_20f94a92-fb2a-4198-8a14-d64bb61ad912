package cn.uone.business.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.base.ChargeTypeEnum;
import cn.uone.bean.entity.business.base.BaseChargeEntity;
import cn.uone.bean.entity.business.base.BaseChargeItemEntity;
import cn.uone.business.base.dao.BaseChargeItemDao;
import cn.uone.business.base.service.IBaseChargeItemService;
import cn.uone.business.base.service.IBaseChargeService;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Service
public class BaseChargeItemServiceImpl extends ServiceImpl<BaseChargeItemDao, BaseChargeItemEntity> implements IBaseChargeItemService {

    @Autowired
    private IBaseChargeService baseChargeService;

    @Override
    public BaseChargeItemEntity getByCondition(BaseChargeItemEntity entity) {
        QueryWrapper<BaseChargeItemEntity> queryWrapper = new QueryWrapper();
        if (StrUtil.isNotEmpty(entity.getChargeId())) {
            queryWrapper.eq("charge_id", entity.getChargeId());
        }
        if (StrUtil.isNotEmpty(entity.getContractType())) {
            queryWrapper.eq("contract_type", entity.getContractType());
        }
        if (ObjectUtil.isNotNull(entity.getOrganize())) {
            queryWrapper.eq("is_organize", entity.getOrganize());
        }
        if (ObjectUtil.isNotNull(entity.getEnable())) {
            queryWrapper.eq("is_enable", entity.getEnable());
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BaseChargeItemEntity> findByCondition(BaseChargeItemEntity entity) {
        QueryWrapper<BaseChargeItemEntity> queryWrapper = new QueryWrapper();
        if (StrUtil.isNotEmpty(entity.getChargeId())) {
            queryWrapper.eq("charge_id", entity.getChargeId());
        }
        if (StrUtil.isNotEmpty(entity.getContractType())) {
            queryWrapper.eq("contract_type", entity.getContractType());
        }
        if (ObjectUtil.isNotNull(entity.getOrganize())) {
            queryWrapper.eq("is_organize", entity.getOrganize());
        }
        if (ObjectUtil.isNotNull(entity.getEnable())) {
            queryWrapper.eq("is_enable", entity.getEnable());
        }
        queryWrapper.orderByAsc("name");
        return this.list(queryWrapper);
    }

    public BaseChargeItemEntity getChargeItem(String projectId, String contractType, String isOrg, ChargeTypeEnum type) throws BusinessException {
        BaseChargeEntity baseChargeEntity = new BaseChargeEntity();
        baseChargeEntity.setProjectId(projectId);
        baseChargeEntity.setType(type.getValue());
        BaseChargeEntity charge = baseChargeService.getByCondition(baseChargeEntity);
        if(ObjectUtil.isNull(charge)){
            throw new BusinessException(type.getName()+"公式未配置");
        }
        BaseChargeItemEntity chargeCondition = new BaseChargeItemEntity();
        chargeCondition.setChargeId(charge.getId());
        chargeCondition.setContractType(contractType);
        chargeCondition.setEnable(true);
        if(BaseConstants.BOOLEAN_OF_TRUE.equals(isOrg)){
            chargeCondition.setOrganize(true);
        }else{
            chargeCondition.setOrganize(false);
        }
        BaseChargeItemEntity chargeItem = this.getByCondition(chargeCondition);
        if(ObjectUtil.isNull(chargeItem)){
            throw new BusinessException(type.getName()+"公式未配置");
        }
        return chargeItem;
    }

}
