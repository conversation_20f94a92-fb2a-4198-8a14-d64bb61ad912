package cn.uone.business.kingdee.controller;


import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptItemSearchVo;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptItemVo;
import cn.uone.business.kingdee.service.IKingdeeReceiptItemService;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@RestController
@RequestMapping("/kingdee-receipt-item-entity")
public class KingdeeReceiptItemController extends BaseController {
    @Autowired
    private IKingdeeReceiptItemService kingdeeReceiptItemService;
    /**
     * 列表
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, KingdeeReceiptItemSearchVo searchVo) {
        RestResponse response = new RestResponse();
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo",searchVo);
        return response.setSuccess(true).setData(kingdeeReceiptItemService.getVoPage(page,map));
    }

    @RequestMapping(value = {"/export"}, method = RequestMethod.POST)
    public void export(HttpServletResponse response, KingdeeReceiptItemSearchVo searchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo",searchVo);
        List<KingdeeReceiptItemVo> list = kingdeeReceiptItemService.getVoList(map);
        beans.put("list", list);
        ExcelRender.me("/excel/export/kingdeeReceipt.xlsx").beans(beans).render(response);
    }
}
