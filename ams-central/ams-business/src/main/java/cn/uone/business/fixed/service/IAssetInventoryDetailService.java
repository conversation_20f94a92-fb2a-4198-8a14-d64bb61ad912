package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.AssetInventoryDetailEntity;
import cn.uone.bean.entity.business.fixed.vo.AssetInventoryDetailVo;
import cn.uone.bean.entity.business.fixed.vo.FixedPropertyVo;
import cn.uone.bean.entity.business.fixed.vo.InventoryStatisticsVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产盘点详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IAssetInventoryDetailService extends IService<AssetInventoryDetailEntity> {

    IPage<AssetInventoryDetailEntity> page(Page page, AssetInventoryDetailEntity entity);

    IPage<FixedPropertyVo> pageList(Page page, Map<String, Object> map);

    List<FixedPropertyVo> getPropertyList(Map<String, Object> map);

    boolean updateDetailInventory(FixedPropertyVo fixedPropertyVo);

    List<AssetInventoryDetailEntity> getList(Map<String, Object> map);

    boolean deleteDetailByInventoryId(String inventoryId);

    int updateByCode(String state,String id,String code);

    int checkByCode(String id,String code);

    InventoryStatisticsVo getQuantity(String inventoryId);
}
