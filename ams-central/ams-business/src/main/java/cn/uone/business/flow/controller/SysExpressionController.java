package cn.uone.business.flow.controller;


import cn.uone.bean.entity.business.flow.SysExpressionEntity;
import cn.uone.business.flow.service.ISysExpressionService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 流程表达式 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@RestController
@RequestMapping("/flow/sys-expression-entity")
public class SysExpressionController extends BaseController {
    @Resource
    private ISysExpressionService iSysExpressionService;
    /**
     * 获取信息
     *
     * @param entity
     * @return 流程表达式列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, SysExpressionEntity entity) {
        IPage<SysExpressionEntity> iPage = iSysExpressionService.page(page, entity);

        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 流程表达式列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        SysExpressionEntity info = iSysExpressionService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param entity 参数
     * @return 流程表达式列表
     */
    @PostMapping("/save")
    public RestResponse save(SysExpressionEntity entity) {
        if(iSysExpressionService.save(entity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param entity 参数
     * @return 流程表达式列表
     */
    @PostMapping("/edit")
    public RestResponse edit(SysExpressionEntity entity) {
        if(iSysExpressionService.updateById(entity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 流程表达式列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iSysExpressionService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
//    @RequestMapping("/importRfid")
//    public RestResponse importRfid(@RequestParam("file") MultipartFile file) {
//        if (file.isEmpty()) {
//            return RestResponse.failure("请选择上传文件");
//        }
//        try {
//            List<SysExpressionVo> rfidEntities  = ExcelDataUtil.importData(file.getInputStream(), SysExpressionVo.class);
//            if(CollectionUtils.isEmpty(rfidEntities)){
//                return RestResponse.failure("读取excel异常");
//            }else {
//                //查重
//                List<String> codes = rfidEntities.stream().map(SysExpressionVo::getCode).collect(Collectors.toList());
//                QueryWrapper<SysExpressionEntity> queryWrapper = new QueryWrapper<>();
//                queryWrapper.in("t_fixed_rfid.code",codes);
//
//                //单独String集合
//                List<String> collect = codes.stream().filter(i -> !Objects.equals(i, ""))               // list 对应的 Stream 并过滤""
//                        .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
//                        .entrySet().stream()                       // 所有 entry 对应的 Stream
//                        .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
//                        .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
//                        .collect(Collectors.toList());
//                if(!collect.isEmpty()&& collect.get(0)!=null){
//                    return RestResponse.failure("标签编号:"+collect+"重复了");
//                }
//
//                List<SysExpressionEntity> rfidEntityList = iSysExpressionService.list(queryWrapper);
//                if(!rfidEntityList.isEmpty()){
//                    List<String> codes2 = rfidEntityList.stream().map(SysExpressionEntity::getCode).collect(Collectors.toList());
//                    return RestResponse.failure("标签编号:"+codes2+"=已存在");
//                }
//                int i =0;
//                List<SysExpressionEntity> list = new ArrayList<>();
//                for(SysExpressionVo vo :rfidEntities){
//                    i++;
//                    if(StringUtils.isEmpty(vo.getCode())){
//                        return RestResponse.failure("第:"+i+"行,标签编号不能为空");
//                    }
//                    if(StringUtils.isEmpty(vo.getLabelSta())){
//                        vo.setLabelSta("1");
//                    }else {
//                        //状态 1=未使用,2=已使用,3=报废
//                        if("未使用".equals(vo.getLabelSta())){
//                            vo.setLabelSta("1");
//                        }else if("已使用".equals(vo.getLabelSta())){
//                            vo.setLabelSta("2");
//                        }else if("报废".equals(vo.getLabelSta())){
//                            vo.setLabelSta("3");
//                        }else {
//                            vo.setLabelSta("1");
//                        }
//                    }
//                    SysExpressionEntity SysExpression = new SysExpressionEntity();
//                    BeanUtil.copyProperties(vo,SysExpression);
//                    list.add(SysExpression);
//                }
//                iSysExpressionService.saveBatch(list);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//            return RestResponse.failure("读取excel异常");
//        }
//        return RestResponse.success("导入成功");
//    }
//
//    @UoneLog("导出Rfid模板")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws BusinessException {
//        Map<String, Object> beans = Maps.newHashMap();
//        ExcelRender.me("/excel/import/SysExpression.xls").beans(beans).render(response);
//    }
}
