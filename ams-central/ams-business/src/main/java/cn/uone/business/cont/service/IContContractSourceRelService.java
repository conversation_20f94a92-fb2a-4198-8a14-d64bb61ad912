package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo;
import cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * 合同房源关系
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IContContractSourceRelService extends IService<ContContractSourceRelEntity> {

    /**
     * 新增合同房源关系
     *
     * @param contract
     * @param source
     * @return
     * @throws Exception
     */
    ContContractSourceRelEntity addContContractSourceRel(ContContractEntity contract, ResSourceEntity source, BigDecimal price, BigDecimal cashPledge, BigDecimal subsidySum, List<ContRentLadderEntity> rentLadders) throws Exception;


    /**
     * 删除合同房源关系
     *
     * @return
     * @throws Exception
     */
    void removeContContractSourceRel(String contractId,String sourceId);

    /**
     * 查看合同房源关系
     *
     * @param id
     * @return
     * @throws Exception
     */
    ContContractSourceRelEntity getContContractSourceRel(String id) throws Exception;

    /**
     * 查询房源信息
     *
     * @param map
     * @return
     * @throws Exception
     */
    List<ContContractSourceRelVo> selectByVo(Map<String, Object> map) throws Exception;

    /**
     * 查询房源信息
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ContContractSourceRelBo> selectByContractId(String contractId);

    /**
     * 查询合同房源关系（关联租金表）
     *
     * @param map
     * @return
     * @throws Exception
     */
    List<ContContractSourceRelVo> query(Map<String, Object> map) throws Exception;

    /**
     * 员工入住
     *
     * @param page
     * @param map
     * @return
     */
    IPage<ContContractSourceRelVo> selectPageByStaffCheckIn(Page page, Map<String, Object> map) throws Exception;

    ContContractSourceRelEntity getByContractIdAndSourceId(String contractId, String sourceId);

    List<ContContractSourceRelEntity> getListByContractId(String contractId);

    ContContractSourceRelEntity selectByCondition(Map<String,Object> map);

    List<ContContractSourceRelEntity> getUnCheckOut(String contractId);

    /**
     * 批量更改结算状态
     * @param contractId
     * @param sourceId
     * @param settleState
     */
    void batchChangeSettleState(String contractId,String sourceId,String settleState);

    void batchChangeSettleState(String contractId,String sourceId);

    List<ContContractSourceRelEntity> findByContractId(String id);

    void setRefundDepost(String contractId,String sourceId);

    void setRefundDepostToNull(String contractId,String sourceId);

    void setRefundDepostToZero(String contractId,String sourceId);

    ContContractSourceRelEntity addContMultiSourceRel(ContContractEntity contract, String sourceIds, BigDecimal price, BigDecimal cashPledge, BigDecimal subsidySum, List<ContRentLadderEntity> rentLadders) throws Exception;
}
