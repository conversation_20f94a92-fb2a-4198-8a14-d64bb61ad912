package cn.uone.business.common.service;

import cn.uone.application.vo.MiniUnionPayItemVo;
import cn.uone.bean.entity.business.bil.BilInvestEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * Created by xmlin on 2019-01-12.
 */
public interface IXmgjjService {

    RestResponse xmgjjdkTask(String contractId);

    RestResponse xmgjjdzTask();
}
