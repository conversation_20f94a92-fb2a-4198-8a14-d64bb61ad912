package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.dto.ContractDTO;
import cn.uone.bean.entity.business.cont.vo.AlterPriceVo;
import cn.uone.bean.entity.business.cont.vo.ContCheckInUserVo;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.cont.vo.ContractCountVo;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.business.sys.vo.RenterVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.parameter.SignContractVo;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IContContractService extends IService<ContContractEntity> {

    //合同的营业执照，同步至租客信息中
    void synImg(String id);

    List<ContContractVo> queryList(Map<String, Object> map);


    /**
     * 查看合同
     * 机构合同退租页面使用
     *
     * @param map
     * @return
     */
    ContContractVo getContractByCheckOut(Map<String, Object> map) throws Exception;


    /**
     * 更新合同
     *
     * @param contContractEntity
     * @return
     * @throws Exception
     */
    int updateContContract(ContContractEntity contContractEntity) throws Exception;


    /**
     * 根据合同模板id查询是否存在已关联合同
     *
     * @param contractTempletId
     * @return
     * @throws Exception
     */
    Boolean existContContractByContractTempId(String contractTempletId) throws Exception;

    /**
     * 查询合同
     *
     * @param page
     * @param map
     * @return
     * @throws Exception
     */
    IPage<ContContractVo> selectContContract(Page<ContContractEntity> page, Map<String, Object> map) throws Exception;

    IPage<ContContractVo> releaseList(Page<ContContractEntity> page, Map<String, Object> map);

    List<ContContractVo> releaseWaitRenterConfirmList(Map<String, Object> map);

    IPage<ContContractVo> selectContContractByUser(Page<ContContractEntity> page, Map<String, Object> map) throws Exception;

    /**
     * 查询合同
     *
     * @param map
     * @return
     * @throws Exception
     */
    List<ContContractVo> selectContContract(Map<String, Object> map) throws Exception;

    List<ContContractVo> getRoomMateList(Map<String, Object> map) throws Exception;

    List<ContContractVo> exportContContract(Map<String, Object> map) throws Exception;

    List<ContContractVo> exportSourceContract(Map<String, Object> map) throws Exception;

    /**
     * 根据签约用户id查询合同
     *
     * @param map
     * @return
     * @throws Exception
     */
    List<ContContractVo> getContContractBySignerId(Map<String, Object> map) throws Exception;

    /**
     * 查询机构合同（分页、项目权限）
     *
     * @param page
     * @param map
     * @return
     * @throws Exception
     */
    IPage<ContContractVo> selectByOrganize(Page<ContContractEntity> page, Map<String, Object> map) throws Exception;

    /**
     * 新增/修改机构合同
     *
     * @param contContractEntity
     * @return
     * @throws Exception
     */
    void saveOrUpdateContract(ContContractEntity contContractEntity) throws Exception;

    void auditContract(ContContractEntity contract, ResSourceEntity source, RenterEntity renter, boolean isRecord) throws Exception;

    ContractCountVo countContractNum();
    ContractCountVo countContractNum(Map<String,String> map);

    List<String> getInReviewContByHomePage (String projectId);
    List<String> getExpireByHomePage (String projectId);

    /**
     * 查看合同
     *
     * @param id
     * @return
     * @throws Exception
     */
    ContContractVo selectContractById(String id) throws Exception;

    void deleteContract(String contractId);

    ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate) throws Exception;

    ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate,String contractId) throws Exception;

    ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate, String contractId, AlterPriceVo alterPriceVo) throws Exception;

    ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate,Date freeStartDate ,Date freeEndDate , String contractId, AlterPriceVo alterPriceVo) throws Exception;

    /**
     * 查询合同分页-XX公寓( 非短租合同)
     *
     * @param page
     * @return
     * @throws Exception
     */
    IPage<ContContractVo> selectPageByComun(Page page, String renterId,Map map);

    IPage<ContContractVo> selectPageByIntention(Page page, String renterId,Map map);

    /**
     * 查询合同分页-管家审核签约
     *
     * @return
     * <AUTHOR>
     * @date 2019-01-14 14:20
     * @Param:
     */
    IPage<Map<String, Object>> selectPageBySign(Map<String, Object> map, Page page);

    /**
     * 查询合同分页-企业端
     *
     * @param page
     * @param contractEntity
     * @return
     */
    IPage<ContContractEntity> selectPageByPass(Page page, ContContractVo contractEntity) throws Exception;

    ContContractEntity getContractBySourceId(String sourceId);

    ContContractEntity getValidContractBySourceId(String sourceId);

    ContContractEntity getUnValidContractBySourceId(String sourceId);

    List<ContContractVo> getServicePhoneByRenterId(String renterId);

    Map<String,Object> utContractInfo(String contractId,String sourceId);

    IPage<ContContractVo> getNameAndValue(Page page,String renterId);
    /**
     * 签约合同pdf生成
     *
     * @param contractId
     * @throws Exception
     */
    SysFileEntity contractPDF(String contractId) throws Exception;

    ContContractEntity signCar(SignContractVo vo) throws Exception;
    /**
     * 生成宠物协议
     *
     * @param contractEntity
     */
    void createPetPDF(ContContractEntity contractEntity) throws Exception;


    IPage<ContContractVo> getContractListBySourceId(Page page, String sourceId,Date date);

    void contractExpireRemind(Map<String, Object> map);

    void cancelContract(String contractId);

    List<ContContractEntity> getRentContractList();

    ContContractEntity getContractByReleaseId(String releaseId);

    String getMaxPaperCode(String preCode);

    File downLoadFile(String zipPath, List<ContContractVo> list);

    ContContractEntity getByPlatformCode(String platformCode);

    /**
     *      续租条件判断
     *    code   -1 不能续签            1可以续签
     *    msg    为不能续签原因         msg此时不重要
     * @param contractId
     * @return
     * @throws Exception
     */
    Map<String,String> judgeCanReletByContractId(String contractId) throws Exception;

    boolean isSameOrderMode(String contractId);

    /**
     * 获取租金
     */
    BigDecimal getPrice(ContContractEntity contract, RenterEntity renter, String sourceId) throws Exception;

    ContContractEntity getOldContractByNewContractId(String newContractId);

    List<ContContractEntity> queryOldContract();


    /**
     * 获取到期1天的续租合同
     */
    List<ContContractEntity> getCheckout1Day();

    RenterEntity judgeRenter(String tel, RenterEntity renter, String name, String idType, String idNo) throws Exception;

    RenterEntity judgeRenter2(String tel, RenterEntity renter, String name, String idType, String idNo,String email,String address) throws Exception;

    void saveCheckInUser(@RequestParam(required = false) List<MultipartFile> tzrIDCardFiles, @RequestParam(required = false) List<MultipartFile> tzrTemporaryFiles, List<ContCheckInUserVo> contCheckInUserList, String sourceId, String tel, ContContractSourceRelEntity contractSourceRelEntity, int idnum, int tenum, String value) throws Exception;

    String addOrganizationContractExamine(ContractDTO contractDTO);

    String addShortOrgContractExamine(ContractDTO contractDTO);


    String addContractExamine(ContractDTO contractDTO);

    void auditMsg(String tel, String name) throws Exception;

    List<ContContractVo> getContractBySignerId(Map<String, Object> map) throws Exception ;

    List<SelectVo> getSourceIdList(String renterId) ;


    void audit(String contractId,String auditResult) throws Exception;

    void audited(ContContractEntity contract) throws Exception;

    /**
     * 上传法大大
     * @param contract
     * @param projectInfo
     * @param renter
     * @return
     * @throws Exception
     */
    RestResponse uploadFdd(ContContractEntity contract, ResProjectInfoEntity projectInfo, RenterEntity renter) throws Exception;

    /**
     * 获取机构合同生成固耗费用合同
     * @return
     * @throws BusinessException
     */
    List<ContContractEntity> getOrgFixContract() throws BusinessException;

    void beenCancel(String id, String remark) throws BusinessException;

    void sortAudit(String contractId,String auditResult) throws Exception;

    String getHydropowerStartTime(String contractId, String type);

    IPage<ContContractVo> getExpirings(Map<String, Object> map,Page page);

    IPage<ContContractVo> selectPageByEmp(Page page, String userId,String contractState);

    ContContractEntity getEffectiveContract(String renterId);

    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate,AlterPriceVo alterPriceVo) throws Exception ;

    List<RenterVo> getRenterList(String projectId , String partitionId ,String floor,String sourceId);

    ContContractEntity generateMultiSourceContract(RenterEntity renter, ContFrameContractEntity frameContract, ContTempEntity temp, Date startDate, Date endDate, String sourceIds, AlterPriceVo alterPriceVo) throws Exception;


    IPage<ContContractVo> getByUnsigned(Page page,ContContractVo vo);

    String getLastPaperCode(String projectId);
    String getLastYxPaperCode(String projectId);

    String generatePaperCode(String projectId);
    String generateYxxyPaperCode(String projectId);

    List<ContContractEntity> getToAuditContractList(Map<String, Object> map);

    int repetitionByPaperCode(String paperCode);

    IPage<Map<String, Object>> selectPageByIntent(Page page, Map<String, Object> map);

    Map<String,Object> getIntentBySourceIdAndRenterId(String sourceId,@Param("renterId") String renterId);

    List<Map<String,String>> getContByRenter(String renterId);

    String generateGjjPaperCode(String projectId);

    int getRenterNum(@Param("cityCode") String cityCode);

    List<Map<String,Object>> getMonthSignCounts();

    List<Map<String,Object>> countRentConts(String year);

    List<Map<String,Object>> selectContractsForRecord(Map<String,Object> map);

    ContContractVo getContractInfoById(String id);

    BigDecimal countRentContsArea();


    List<ContContractVo> expiringIn30days(List<String> projectCodes);

    RestResponse autoRenterConfirm();
}
