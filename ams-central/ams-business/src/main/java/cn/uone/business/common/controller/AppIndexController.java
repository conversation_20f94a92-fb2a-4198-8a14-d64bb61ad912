package cn.uone.business.common.controller;

import cn.uone.bean.entity.business.bil.vo.BilCountVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.vo.ContractCountVo;
import cn.uone.bean.entity.business.res.vo.ResSourceCount;
import cn.uone.bean.entity.business.sale.vo.CustomerCountVo;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.cont.service.IContContractFileRelService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.flow.service.IFlowTaskService;
import cn.uone.business.res.service.IResRepairService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/app")
public class AppIndexController extends BaseController {

    @Autowired
    IContContractService contContractService;
    @Autowired
    ISaleCustomerService customerService;
    @Autowired
    IResRepairService repairService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IResSourceService sourceService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private ISysParaFegin sysParaFegin;
    @Autowired
    private IBilTransferService transferService;
    @Autowired
    IFlowTaskService flowTaskService;
    @Autowired
    IContContractFileRelService contContractFileRelService;

    @Value("${spring.profiles.active}")
    private String active;

    @RequestMapping("/getCount")
    public RestResponse getCount(String projectId) {
        Map<String,String> para=Maps.newHashMap();
        para.put("projectId",projectId);
        ContractCountVo cont=contContractService.countContractNum(para);
        /*try {
            Map<String,Object> taskMap = flowTaskService.todoMap(true,"新签约审批");
            List<String> keysList = taskMap.keySet().stream().collect(Collectors.toList());
            Map<String,Object> map = Maps.newHashMap();
            map.put("projectId",projectId);
            map.put("contractIds",keysList);
            List<ContContractEntity> contList = contContractService.getToAuditContractList(map);
            cont.setTobeNum(contList==null?0:contList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        //房源看板数据（总，空，租数，即将到期，今日预定数，今日签约数附列表）
        ResSourceCount sourceCount=sourceService.getStatistics(para);
        CustomerCountVo customer = customerService.countCustomer(projectId);
        Map<String,Object> maps = Maps.newHashMap();
        maps.put("projectId",projectId);
        BilCountVo bilCountVo = bilOrderService.getMonthData(maps);
        BilCountVo bilCountVo2 = bilOrderService.getStatistics(para);
        para.put("orderType","10");
        BilCountVo count = bilOrderService.getReserveCount(para);
        bilCountVo2.setReserve(count.getReserve());
        Map<String, Object> fault = repairService.countRepairNum(projectId);
        Map<String, Object> overdueFee = bilOrderService.getOverdueFee(projectId);
        Map<String, Object> rentOutState = resSourceService.getLeasedByNow(projectId);
        Map<String, Object> receivable = transferService.getReceivable(projectId);
        Map<String, Object> received = transferService.getReceived(projectId);
        //公积金授权统计
        Map<String, Object> xmgjj = contContractFileRelService.getStatistics(projectId);
//        Map<String, Object> fail = deviceLogService.countFailNum();
//        ResPatrolSearchVo vo=new ResPatrolSearchVo();
//        boolean searchAll= userFegin.getUserPermission("res:patrol:searchAll", UoneSysUser.id());
//        vo.setSearchAll(searchAll);
//        vo.setUserId(UoneSysUser.id());
//        ResPatrolVo patrol= patrolService.statistics(vo);
        //轨道生效合同套数=市场化生效套数+员工生效间数/2（半套进1）
        cont.setTotalEffectedNum(cont.getShEffectedNum()+Math.round(cont.getYgEffectedNum()/2));
        //轨道待生效合同套数=市场化待生效套数+员工待生效间数/2（半套进1）
        cont.setTotalToEffectNum(cont.getShToEffectNum()+Math.round(cont.getYgToEffectNum()/2));
        //轨道今日签约套数=今日市场化签约套数+今日员工签约间数/2（半套进1）
        cont.setTodayNum(cont.getTodayShSignNum()+Math.round(cont.getTodayYgSignNum()/2));
        Map<String, Object> map = Maps.newHashMap();
        map.put("cont", cont);
        map.put("customer", customer);
        map.put("fault", fault);
//        map.put("fail", fail);
//        map.put("patrol", patrol);
        map.put("monthBil",bilCountVo);
        map.put("bil",bilCountVo2);
        map.put("source",sourceCount);
        map.put("arrearage", overdueFee.get("arrearage"));
        map.put("overdue",overdueFee.get("overdue"));
        map.put("leasedNumber",rentOutState.get("leasedNumber"));
        map.put("leisureNumber",rentOutState.get("leisureNumber"));
        map.put("receivable",receivable);
        map.put("received",received);
        map.put("xmgjj",xmgjj);
        return RestResponse.success().setData(map);
    }

    @RequestMapping("/statistics")
    public RestResponse statistics() {
        String projectId= UoneHeaderUtil.getProjectId();
        Map<String,String> para=Maps.newHashMap();
        para.put("projectId",projectId);
        String days = sysParaFegin.getByCode("CONTRACT_EXPIRE_DAYS");
        if(days==null){
            days = "7";
        }
        para.put("days",days);
        //合同看板数据（在租总合同数，15天内到期合同数）
        ContractCountVo contractCountVo=contContractService.countContractNum(para);
        //财务看板数据（逾期数及列表，催缴功能，入账金额，应收金额，已收金额）
        BilCountVo bilCountVo=bilOrderService.getStatistics(para);
        //房源看板数据（总，空，租数，即将到期，今日预定数，今日签约数附列表）
        ResSourceCount sourceCount=sourceService.getStatistics(para);
        //预约（总数，已跟进，未跟进）
        CustomerCountVo customer = customerService.countCustomer(para);
        //客户服务（已处理数，待处理数）
        para.put("repairType","");//判空为1,3
        Map<String, Object> repair = repairService.countRepairNum(para);
        para.put("repairType","2");
        Map<String, Object> complain = repairService.countRepairNum(para);
        para.put("repairType","4");
        Map<String, Object> suggestion = repairService.countRepairNum(para);
        Map<String, Object> map = Maps.newHashMap();
        map.put("cont", contractCountVo);
        map.put("bil",bilCountVo);
        map.put("source",sourceCount);
        map.put("customer", customer);
        map.put("repair", repair);
        map.put("complain",complain);
        map.put("suggestion",suggestion);
        //new
        Map<String,Object> m = resSourceService.getBySixMonths(UoneHeaderUtil.getProjectId());
        List<String> date = getSixMonths();
        List<Object> leased = new ArrayList<>();
        List<Object> leisure = new ArrayList<>();
        List<Object> lettingRate = new ArrayList<>();
        if(m==null){
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
            leased.add(0);
            leisure.add(0);
            lettingRate.add(0);
        }else {
            leased.add(m.get("fiveLeased"));
            leisure.add(m.get("fiveLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("fiveLeased").toString()), Integer.valueOf(m.get("fiveLeisure").toString())));
            leased.add(m.get("fourLeased"));
            leisure.add(m.get("fourLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("fourLeased").toString()), Integer.valueOf(m.get("fourLeisure").toString())));
            leased.add(m.get("threeLeased"));
            leisure.add(m.get("threeLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("threeLeased").toString()), Integer.valueOf(m.get("threeLeisure").toString())));
            leased.add(m.get("twoLeased"));
            leisure.add(m.get("twoLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("twoLeased").toString()), Integer.valueOf(m.get("twoLeisure").toString())));
            leased.add(m.get("lastLeased"));
            leisure.add(m.get("lastLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("lastLeased").toString()), Integer.valueOf(m.get("lastLeisure").toString())));
            leased.add(m.get("nowLeased"));
            leisure.add(m.get("nowLeisure"));
            lettingRate.add(getLettingRate(Integer.valueOf(m.get("nowLeased").toString()), Integer.valueOf(m.get("nowLeisure").toString())));
        }
        map.put("date",date);
        map.put("leased",leased);
        map.put("leisure",leisure);
        map.put("lettingRate",lettingRate);
        return RestResponse.success().setData(map);
    }

    @RequestMapping("/getActive")
    @UonePermissions
    public RestResponse getActive(){
        return RestResponse.success().setData(active);
    }

    @RequestMapping("/getByMonths")
    @UonePermissions
    public RestResponse getByMonths(Integer startYear,Integer startMonth,Integer endYear,Integer endMonth){
        Map<String, Object> map = Maps.newHashMap();
        LocalDate startDate = LocalDate.of(startYear,startMonth,1);
        LocalDate endDate = LocalDate.of(endYear,endMonth,1);
        List<String> dates = new ArrayList<>();
        List<Object> leased = new ArrayList<>();
        List<Object> leisure = new ArrayList<>();
        List<Object> vacancyRate = new ArrayList<>();
        long numOfDaysBetween = ChronoUnit.MONTHS.between(startDate, endDate);
        for (int i = 0; i <= numOfDaysBetween; i++) {
            LocalDate date = startDate.plusMonths(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            dates.add(date.format(formatter));
            Map<String,Object> m  = resSourceService.getByMonths(Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()),UoneHeaderUtil.getProjectId());
            vacancyRate.add(getLettingRate(Integer.valueOf(m.get("leisure").toString()), Integer.valueOf(m.get("leased").toString())));
            leased.add(m.get("leased"));
            leisure.add(m.get("leisure"));
        }
        map.put("dates",dates);
        map.put("leased",leased);
        map.put("leisure",leisure);
        map.put("vacancyRate",vacancyRate);
        return RestResponse.success().setData(map);
    }




    public List<String> getSixMonths(){
        List<String> resultList = new ArrayList<String>();
        Calendar cal = Calendar.getInstance();
        //近六个月
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH)-6); //要先+1,才能把本月的算进去
        for(int i=0; i<6; i++){
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH)+1); //逐次往前推1个月
            resultList.add(String.valueOf(cal.get(Calendar.YEAR))
                    +"-"+ (cal.get(Calendar.MONTH)+1 < 10 ? "0" +
                    (cal.get(Calendar.MONTH)+1) : (cal.get(Calendar.MONTH)+1)));
        }
        return resultList;
    }

    public double  getLettingRate(Integer x,Integer y){
        String percent = "";
        double xx = x * 100.0;
        double yy = y * 100.0 ;
        double zz = xx / (xx+yy);
        String str = String.format("%.2f",zz*100.00);
        double d =  Double.parseDouble(str);
//        DecimalFormat df = new DecimalFormat("##.00%");
//        if(Math.abs(zz)<0.000000000001){
//            percent = "0.00%";
//        } else {
//            percent = df.format(zz);
//        }
        return d;
    }
}
