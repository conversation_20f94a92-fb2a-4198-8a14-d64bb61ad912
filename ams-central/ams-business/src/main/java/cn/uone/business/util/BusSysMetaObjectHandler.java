package cn.uone.business.util;

import cn.hutool.core.util.BooleanUtil;
import cn.uone.mybatis.config.SysMetaObjectHandler;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.util.UoneSysUser;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * mybatis 自动填充
 */
@Component
public class BusSysMetaObjectHandler extends SysMetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        if (UoneSysUser.ShiroUser() != null && LoginType.USER.equals(UoneSysUser.loginType())) {
            setFieldValByName("createBy", UoneSysUser.id(), metaObject);
        }
        if (UoneSysUser.ShiroUser() != null && LoginType.USER.equals(UoneSysUser.loginType())) {
            setFieldValByName("updateBy", UoneSysUser.id(), metaObject);
        }

        super.insertFill(metaObject);
    }

    //更新填充
    @Override
    public void updateFill(MetaObject metaObject) {
        if (UoneSysUser.ShiroUser() != null && LoginType.USER.equals(UoneSysUser.loginType())) {
            setFieldValByName("updateBy", UoneSysUser.id(), metaObject);
        }
        super.updateFill(metaObject);
    }

    public static void main(String[] args) {

        System.out.println(BooleanUtil.isTrue(null));
        String r = "font-family:[\\s\\S]+?;";

        String r2 = "font-family:[\\s\\S]+?\"";
        String r3 = "font-family:[\\s\\S]+?:\"";


        System.out.println(Pattern.matches(r, "font-family:宋体;"));
        String s = "font-family:宋体;1233";

        System.out.println(s.replaceAll(r, ""));

        System.out.println(Pattern.matches(r2, "font-family:宋体;\""));

    }
}
