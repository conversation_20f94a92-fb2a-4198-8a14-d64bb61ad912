package cn.uone.business.res.service.impl;

import cn.uone.bean.entity.business.res.ResCollectEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeHotEntity;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeHotVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeSearchVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeVo;
import cn.uone.business.res.dao.ResHouseTypeDao;
import cn.uone.business.res.dao.ResHouseTypeHotDao;
import cn.uone.business.res.service.IResHouseTypeHotService;
import cn.uone.business.res.service.IResHouseTypeService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class ResHouseTypeHotServiceImpl extends ServiceImpl<ResHouseTypeHotDao, ResHouseTypeHotEntity> implements IResHouseTypeHotService {
    @Override
    public IPage<ResHouseTypeHotVo> hotTypePage(Page page,@Param("map") Map<String,Object> map) {
        return baseMapper.hotTypePage(page,map);
    }

    @Override
    public List<ResHouseTypeHotVo> hotTypes(Map<String,Object> map) {
        return baseMapper.hotShowList(map);
    }

    @Override
    public List<ResHouseTypeHotVo> collectShow(ResCollectEntity searchVo) {
        return baseMapper.collectShowTwo(searchVo);
    }

}
