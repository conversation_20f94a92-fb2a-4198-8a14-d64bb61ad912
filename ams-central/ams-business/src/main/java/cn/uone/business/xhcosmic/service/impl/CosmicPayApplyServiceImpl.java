package cn.uone.business.xhcosmic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.cosmic.AssistantDataTypeEnum;
import cn.uone.application.enumerate.cosmic.CosmicBaseDataTypeEnum;
import cn.uone.application.enumerate.cosmic.PaidStatusEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.constant.CosmicConstant;
import cn.uone.bean.entity.business.bil.BilCarryOverEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderItemWithOrderVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.cosmic.CosmicLogEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicAccountsPayableEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicBaseDataEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicPayApplyBusinessEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicPayApplyEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicPayApplyInfoEntity;
import cn.uone.bean.entity.business.xhcosmic.vo.PayApplySearchVo;
import cn.uone.bean.entity.tpi.cosmic.BaseSaveVo;
import cn.uone.bean.entity.tpi.cosmic.payApply.PayApplyBusinessEntry;
import cn.uone.bean.entity.tpi.cosmic.payApply.PayApplyEntry;
import cn.uone.bean.entity.tpi.cosmic.payApply.PayApplySaveDto;
import cn.uone.business.bil.dao.BilOrderItemDao;
import cn.uone.business.bil.service.IBilCarryOverService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.biz.dao.BizAccountDao;
import cn.uone.business.cosmic.service.ICosmicLogService;
import cn.uone.business.xhcosmic.dao.CosmicPayApplyDao;
import cn.uone.business.xhcosmic.service.ICosmicBaseDataService;
import cn.uone.business.xhcosmic.service.ICosmicPayApplyBusinessService;
import cn.uone.business.xhcosmic.service.ICosmicPayApplyInfoService;
import cn.uone.business.xhcosmic.service.ICosmicPayApplyService;
import cn.uone.fegin.tpi.cosmic.IPayApplyFeign;
import cn.uone.web.base.RestResponse;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 金蝶(星瀚)系统 付款申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Service
public class CosmicPayApplyServiceImpl extends ServiceImpl<CosmicPayApplyDao, CosmicPayApplyEntity> implements ICosmicPayApplyService {

    @Autowired
    private IPayApplyFeign payApplyFeign;

    @Autowired
    private ICosmicPayApplyInfoService cosmicPayApplyInfoService;

    @Autowired
    private ICosmicPayApplyBusinessService cosmicPayApplyBusinessService;

    @Autowired
    private BilOrderItemDao bilOrderItemDao;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilCarryOverService bilCarryOverService;

    @Autowired
    private BizAccountDao bizAccountDao;

    @Autowired
    private ICosmicBaseDataService cosmicBaseDataService;
    @Autowired
    private ICosmicLogService logService;
    // 创建调度线程池（类成员变量）
    private final ScheduledExecutorService retryExecutor = Executors.newSingleThreadScheduledExecutor();
    private static final Logger log = LoggerFactory.getLogger(CosmicAccountsPayableServiceImpl.class);

    @Override
    @Transactional
    public void saveBatchWithItem(Collection<CosmicPayApplyEntity> payApplyEntities) {
        if (CollUtil.isNotEmpty(payApplyEntities)) {
            saveOrUpdateBatch(payApplyEntities);
            List<CosmicPayApplyInfoEntity> infoItems = new ArrayList<>();
            List<CosmicPayApplyBusinessEntity> businessItems = new ArrayList<>();
            payApplyEntities.forEach(payApply -> {
                payApply.getInfoItems().forEach(item -> {
                    if (StrUtil.isBlank(item.getPayApplyId())) {
                        item.setPayApplyId(payApply.getId());
                    }
                });
                infoItems.addAll(payApply.getInfoItems());
                payApply.getBusinessItems().forEach(item -> {
                    if (StrUtil.isBlank(item.getPayApplyId())) {
                        item.setPayApplyId(payApply.getId());
                    }
                });
                businessItems.addAll(payApply.getBusinessItems());
            });
            if (CollectionUtil.isNotEmpty(infoItems)) {
                cosmicPayApplyInfoService.saveOrUpdateBatch(infoItems);
            }
            if (CollectionUtil.isNotEmpty(businessItems)) {
                cosmicPayApplyBusinessService.saveOrUpdateBatch(businessItems);
            }
        }
    }

    @Override
    public RestResponse push(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return RestResponse.failure("请选择至少一条记录");
        }
        RestResponse restResponse = pushDispose(ids);

        /* 重发机制 */
        if (Boolean.parseBoolean((String)restResponse.get("pushState"))) {
            // 提交异步任务
            retryExecutor.schedule(() -> processRetryLogic(ids), 30, TimeUnit.SECONDS); // 设置30秒延迟:ml-citation{ref="7"
                                                                                        // data="citationList"}
        }
        return restResponse;
    }

    // 分离重试处理逻辑
    private void processRetryLogic(List<String> ids) {
        List<CosmicLogEntity> logList = logService.getByIds(ids, "4");
        List<String> newIds = new ArrayList<>();

        for (CosmicLogEntity log : logList) {
            if ("2".equals(log.getPushStatus()) && log.getRetryFrequency() < 5) {
                newIds.add(log.getDataId());
            }
        }
        if (CollUtil.isNotEmpty(newIds)) {
            push(newIds); // 递归调用
        }
    }


    @Transactional
    public RestResponse pushDispose(List<String> ids) {

        try {
            Collection<CosmicPayApplyEntity> entities = listByIds(ids);

            //填充 付款申请单明细单据体
            List<CosmicPayApplyInfoEntity> infoItems = cosmicPayApplyInfoService.list(
                new LambdaQueryWrapper<CosmicPayApplyInfoEntity>().in(CosmicPayApplyInfoEntity::getPayApplyId, ids));

            Map<String, List<CosmicPayApplyInfoEntity>> infoItemMap =
                infoItems.stream().collect(Collectors.groupingBy(CosmicPayApplyInfoEntity::getPayApplyId));
            entities.forEach(payApply -> payApply.getInfoItems()
                .addAll(infoItemMap.getOrDefault(payApply.getId(), Lists.newArrayList())));

            //
            List<CosmicPayApplyBusinessEntity> businessItems =
                cosmicPayApplyBusinessService.list(new LambdaQueryWrapper<CosmicPayApplyBusinessEntity>()
                    .in(CosmicPayApplyBusinessEntity::getPayApplyId, ids));
            Map<String, List<CosmicPayApplyBusinessEntity>> businessItemMap =
                businessItems.stream().collect(Collectors.groupingBy(CosmicPayApplyBusinessEntity::getPayApplyId));

            //体虫 付款申请单业务单据体
            entities.forEach(payApply -> payApply.getBusinessItems()
                .addAll(businessItemMap.getOrDefault(payApply.getId(), Lists.newArrayList())));

            List<PayApplySaveDto> dtoList = entities.stream().map(this::initPushData).collect(Collectors.toList());
            RestResponse response = payApplyFeign.saveBatch(dtoList);

            if (BooleanUtil.isTrue(response.getSuccess()) && response.get("data") != null) {
                BaseSaveVo vo = JSONObject.from(response.get("data")).toJavaObject(BaseSaveVo.class);
                if (NumberUtil.parseInt(vo.getFailCount()) > 0) {
                    List<String> errorSourceBillNumbers = vo.getResult().stream()
                        .map(baseSaveResultVo -> baseSaveResultVo.getSourceInfo().getSourceBillNumber())
                        .collect(Collectors.toList());
                    //移除推送失败的应收单 此应收单不保存.
                    for (CosmicPayApplyEntity entity : entities) {
                        if (errorSourceBillNumbers.contains(entity.getSourceBillNumber())) {
                            entity.setIsPush(0);
                        }
                    }
                }
                saveBatchWithItem(entities);
            }
            //保存调用日志
            String pushState = logService.saveLog(ids, response, "4");
            return response.setAny("pushState", pushState);
        } catch (Exception e) {
            e.printStackTrace();
            RestResponse errorResponse = new RestResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("推送过程中出现异常: " + e.getMessage());
            //保存调用日志-异常情况
            logService.saveLogForAbnormal(ids, e.getMessage(), "4");
            return errorResponse.setAny("pushState", "false");
        }

    }

    @Override
    @Transactional
    public void cast2PayApplyList(Collection<CosmicAccountsPayableEntity> accountsPayableEntities) {
        //根据收支项目是否参与结算去判断付款类型是参与结算还是不参与结算，收支项目参与结算传01付款，预付款传02预付款，不参与结算传211费用报销，预付款传701费用报销-预付款
        Set<String> sourceBillType = accountsPayableEntities.stream()
            .map(CosmicAccountsPayableEntity::getSourceBillType).collect(Collectors.toSet());
        Set<String> sourceBillNumbers = accountsPayableEntities.stream()
            .map(CosmicAccountsPayableEntity::getSourceBillNumber).collect(Collectors.toSet());
        List<BilOrderItemWithOrderVo> itemWithOrderVoList = bilOrderItemDao.getItemWithOrderVoList(null, null, null, 0, null, "PAYMENT", sourceBillNumbers);
        Map<String, BilOrderItemWithOrderVo> voMap =
            itemWithOrderVoList.stream().collect(Collectors.toMap(BilOrderItemWithOrderVo::getOrderId, Function.identity()));
        Map<String, String> expenseItemMap = Maps.newHashMap();
        CosmicExpenseItemServiceImpl.INIT_MAP.forEach((k, v) -> {
            if (sourceBillType.contains(k)) {
                expenseItemMap.put(k, v.getExpenseItemNumber());
            }
        });
        List<CosmicBaseDataEntity> cosmicBaseDataEntities = cosmicBaseDataService.listByTypeAndNumbers(CosmicBaseDataTypeEnum.EXPENSE_ITEM.getType(), expenseItemMap.values());
        //是否参与结算 true参与 false不参与
        Map<String, CosmicBaseDataEntity> expenseIsSettlementMap = cosmicBaseDataEntities.stream()
            .collect(Collectors.toMap(CosmicBaseDataEntity::getNumber, Function.identity()));

        // 提前批量获取所有订单的付款人ID
        Set<String> payerIds = itemWithOrderVoList.stream()
            .map(item -> item.getBilOrderVo()).filter(Objects::nonNull).map(order -> order.getPayerId())
            .filter(StrUtil::isNotBlank).collect(Collectors.toSet());

        // 批量查询所有银行账户信息
        Map<String, BizAccountEntity> accountMap;
        // 创建额外的银行数据映射，用于存储账户和银行数据的关系
        Map<String, CosmicBaseDataEntity> bankDataMap = Maps.newHashMap();

        if (CollUtil.isNotEmpty(payerIds)) {
            QueryWrapper<BizAccountEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("renter_id", payerIds);
            List<BizAccountEntity> accountEntities = bizAccountDao.selectList(queryWrapper);
            // 对账户信息进行处理，测试用的模拟数据
            // accountEntities.forEach(bizAccount -> {
            //     bizAccount.setBankBranch("中国建设银行股份有限公司永和支行");
            // });
            accountMap = accountEntities.stream()
                .collect(Collectors.toMap(BizAccountEntity::getRenterId, Function.identity(), (v1, v2) -> v1));

            // 收集所有需要查询的银行编号
            Set<String> bankBranchCodes = accountEntities.stream()
                .map(BizAccountEntity::getBankBranch).filter(StrUtil::isNotBlank).collect(Collectors.toSet());

            // 批量查询所有银行基础数据
            List<CosmicBaseDataEntity> bankDataEntities =
                cosmicBaseDataService.listByTypeAndName(CosmicBaseDataTypeEnum.BANK.getType(), bankBranchCodes);
            Map<String, CosmicBaseDataEntity> bankMap = bankDataEntities.stream()
                .collect(Collectors.toMap(CosmicBaseDataEntity::getName, Function.identity(), (v1, v2) -> v1));

            // 将银行信息与账户ID关联，存储在bankDataMap中
            for (BizAccountEntity account : accountEntities) {
                if (StrUtil.isNotBlank(account.getBankBranch()) && bankMap.containsKey(account.getBankBranch())) {
                    bankDataMap.put(account.getRenterId(), bankMap.get(account.getBankBranch()));
                }
            }
        } else {
            accountMap = Collections.emptyMap();
        }

        // 使用预查询的数据进行转换
        List<CosmicPayApplyEntity> entities =
            accountsPayableEntities.stream().map(accountsPayableEntity -> cast2PayApplySingle(accountsPayableEntity,
                expenseIsSettlementMap.get(expenseItemMap.get(accountsPayableEntity.getSourceBillType())),
                voMap.get(accountsPayableEntity.getSourceBillNumber()), accountMap, bankDataMap))
                .filter(Objects::nonNull).collect(Collectors.toList());
        saveBatchWithItem(entities);
    }

    /**
     * 将财务应付单转换为付款申请单
     *
     * @param accountsPayableEntity accountsPayableEntity
     * @param baseData baseData
     * @param itemWithOrderVo itemWithOrderVo
     * @param accountMap 预查询的银行账户信息映射
     * @param bankDataMap 预查询的银行数据映射
     * @return CosmicPayApplyEntity
     */
    private CosmicPayApplyEntity cast2PayApplySingle(CosmicAccountsPayableEntity accountsPayableEntity,
        CosmicBaseDataEntity baseData, BilOrderItemWithOrderVo itemWithOrderVo,
        Map<String, BizAccountEntity> accountMap, Map<String, CosmicBaseDataEntity> bankDataMap) {
        if (itemWithOrderVo == null) {
            return null;
        }
        CosmicPayApplyEntity cosmicPayApply = new CosmicPayApplyEntity();
        BeanUtils.copyProperties(accountsPayableEntity, cosmicPayApply);
        cosmicPayApply.setCreatorNumber(CosmicConstant.CREATOR_NUMBER)
            .setApplyDate(accountsPayableEntity.getBizDate()).setPayeeAmount(itemWithOrderVo.getPayment())
            .setPaymentIdentifyNumber("FKBS01").setPayOrgNumber(CosmicConstant.ORG_NUMBER)
            .setApplyOrgNumber(CosmicConstant.ORG_NUMBER)
            // 默认待支付
            .setPaidStatus(PaidStatusEnum.NO_PAID.getValue());
        String payState = itemWithOrderVo.getBilOrderVo().getPayState();
        PayStateEnum payStatusEnum = PayStateEnum.findPayStatusEnum(payState);
        if (payStatusEnum != null) {
            switch (payStatusEnum) {
                case NOPAY:
                case REFUNDPENDING:
                    cosmicPayApply.setPaidStatus(PaidStatusEnum.NO_PAID.getValue());
                    break;
                case PAYCONFIR:
                case REFUNDED:
                    cosmicPayApply.setPaidStatus(PaidStatusEnum.PAID.getValue());
                    break;
                case REFUNDTOAUDIT:
                case REFUNDAUDITING:
                    cosmicPayApply.setPaidStatus(PaidStatusEnum.PAYING.getValue());
                    break;
            }
        }
        //根据收支项目属性组装对应参数
        if (baseData != null) {
            JSONObject expandData = JSONObject.from(baseData.getExpandDataJson());
            boolean isSettlement = expandData.getBooleanValue("issettlement", true);
            //根据收支项目是否参与结算去判断付款类型是参与结算还是不参与结算，收支项目参与结算传01付款，预付款传02预付款，不参与结算传211费用报销，预付款传701费用报销-预付款
            if (isSettlement) {
                cosmicPayApply.setPaymentTypeNumber("01");
            } else {
                cosmicPayApply.setPaymentTypeNumber("211");
            }
            initBusinessEntryValue(cosmicPayApply, baseData, itemWithOrderVo);
        }

        // 填充付款申请单 info 数据
        initInfoEntryValue(cosmicPayApply, itemWithOrderVo, accountMap, bankDataMap);
        //TODO 没法将我们的银行跟金蝶的对应 这个参数非必传就先不传了
        //initEntryValue(cosmicPayApply,itemWithOrderVo)
        return cosmicPayApply;
    }

    /**
     * 填充付款申请单 业务单据数据
     */
    private void initBusinessEntryValue(CosmicPayApplyEntity cosmicPayApply, CosmicBaseDataEntity baseData, BilOrderItemWithOrderVo itemWithOrderVo) {
        JSONObject expandData = JSONObject.from(baseData.getExpandDataJson());
        List<JSONObject> assList = MapUtil.get(expandData, "assList", new TypeReference<List<JSONObject>>() {});
        CosmicPayApplyBusinessEntity business = new CosmicPayApplyBusinessEntity();
        business.setExpenseItemNumber(baseData.getNumber())
                .setBusPayeeAmount(cosmicPayApply.getPayeeAmount());

        // 使用传统for循环替代lambda，避免出现变量引用问题
        for (JSONObject jsonObject : assList) {
            String valueTypeName = jsonObject.getOrDefault("valueTypeName", "").toString();
            if ("基础资料".equals(valueTypeName)) {
                if (CosmicBaseDataTypeEnum.PROJECT.getDesc().equals(jsonObject.getOrDefault("name", ""))) {
                    if ("AMBH".equals(itemWithOrderVo.getBilOrderVo().getProjectCode())) {
                        business.setProjectNumber(CosmicConstant.PROJECT_NUMBER_AMBH);//固定值
                    }
                    if ("AMYN".equals(itemWithOrderVo.getBilOrderVo().getProjectCode())) {
                        business.setProjectNumber(CosmicConstant.PROJECT_NUMBER_AMYN);//固定值
                    }
                }
            }
            if ("辅助资料".equals(valueTypeName)) {
                AssistantDataTypeEnum assistantDataTypeEnum =
                    AssistantDataTypeEnum.parseEnumByName(jsonObject.getOrDefault("name", "").toString());
                if (assistantDataTypeEnum != null) {
                    switch (assistantDataTypeEnum) {
                        case CONTRACT:
                            business.setContractNumber(CosmicConstant.CONTRACT_NUMBER);//固定值-合同名称公共合同
                            break;
                        case BIZ_TYPE:
                            if ("0".equals(itemWithOrderVo.getBilOrderVo().getPlatform())) {
                                business.setBizTypeNumber(CosmicConstant.SOCIAL_CONTRACT_BIZ_TYPE_NUMBER);
                            } else {
                                business.setBizTypeNumber(CosmicConstant.EMPLOYEE_DORMITORY_CONTRACT_BIZ_TYPE_NUMBER);
                            }
                            break;
                    }
                }
            }
        }
        cosmicPayApply.setBusinessItems(ListUtil.toList(business));
    }

    /**
     * 填充付款申请单 info 数据
     *
     * @param accountMap 预查询的银行账户信息映射
     * @param bankDataMap 预查询的银行数据映射
     */
    private void initInfoEntryValue(CosmicPayApplyEntity cosmicPayApply, BilOrderItemWithOrderVo itemWithOrderVo, Map<String, BizAccountEntity> accountMap, Map<String, CosmicBaseDataEntity> bankDataMap) {

        // 从订单获取付款人ID
        String payerId = itemWithOrderVo.getBilOrderVo() != null ? itemWithOrderVo.getBilOrderVo().getPayerId() : null;

        // 付款申请单明细单据体
        CosmicPayApplyInfoEntity info = new CosmicPayApplyInfoEntity()
            // JSFS13 结算方式 转帐
            .setSettlementTypeNumber("JSFS13").setPayeeAmount(cosmicPayApply.getPayeeAmount()).setPriority("public")
            .setPayeeType("other");

        // 如果有付款人ID，直接从预查询的Map中获取银行账户信息
        if (StrUtil.isNotBlank(payerId) && accountMap.containsKey(payerId)) {
            BizAccountEntity accountEntity = accountMap.get(payerId);
            if (accountEntity != null) {
                info.setPayeeAccBankNum(accountEntity.getCode()) // 设置银行账号
                    .setPayeeName(accountEntity.getName()); // 设置账户名称

                // 从bankDataMap中获取预先查询的银行基础数据
                CosmicBaseDataEntity bankData = bankDataMap.get(payerId);
                if (bankData != null) {
                    info.setPayeeBankNumber(bankData.getNumber());
                    info.setAccountName(accountEntity.getBankBranch());
                }
            }
        }

        cosmicPayApply.setInfoItems(ListUtil.toList(info));
    }

    //讲了收入的流程 不讲付款的流程 咋做啊 兄弟 文档都看不懂 乱得要死
    private PayApplySaveDto initPushData(CosmicPayApplyEntity entity) {
        entity.setIsPush(1);
        PayApplySaveDto dto = new PayApplySaveDto();
        BeanUtils.copyProperties(entity, dto);
        dto.setApplyDate(DateUtil.date(entity.getApplyDate()));
        List<PayApplyEntry> infoEntries = entity.getInfoItems().stream().map(item -> {
            PayApplyEntry entry = new PayApplyEntry();
            BeanUtils.copyProperties(item, entry);
            entry.setUnique(item.getId());
            return entry;
        }).collect(Collectors.toList());
        List<PayApplyBusinessEntry> businessEntries = entity.getBusinessItems().stream().map(item -> {
            PayApplyBusinessEntry businessEntry = new PayApplyBusinessEntry();
            BeanUtils.copyProperties(item, businessEntry);
            return businessEntry;
        }).collect(Collectors.toList());
        return dto.setApplyEntry(infoEntries)
            .setBusinessEntry(businessEntries);
    }

    @Override
    public IPage<CosmicPayApplyEntity> queryPage(Page<CosmicPayApplyEntity> page, PayApplySearchVo searchVo) {
        return page(page,
            new LambdaQueryWrapper<CosmicPayApplyEntity>()
                .like(StrUtil.isNotBlank(searchVo.getSourceBillNumber()), CosmicPayApplyEntity::getSourceBillNumber,
                    searchVo.getSourceBillNumber())
                .eq(searchVo.getIsPush() != null, CosmicPayApplyEntity::getIsPush, searchVo.getIsPush())
                .orderByDesc(CosmicPayApplyEntity::getCreateDate) // 按创建时间降序排序
                .orderByAsc(CosmicPayApplyEntity::getIsPush) // 按推送状态升序排序
        );
    }

    @Override
    public CosmicPayApplyEntity info(String id) {
        CosmicPayApplyEntity entity = getById(id);
        if (entity == null) {
            return null;
        }
        List<CosmicPayApplyBusinessEntity> businessItems = cosmicPayApplyBusinessService.list(
            new LambdaQueryWrapper<CosmicPayApplyBusinessEntity>().eq(CosmicPayApplyBusinessEntity::getPayApplyId, id));
        entity.setBusinessItems(businessItems);

        List<CosmicPayApplyInfoEntity> infoItems = cosmicPayApplyInfoService
            .list(new LambdaQueryWrapper<CosmicPayApplyInfoEntity>().eq(CosmicPayApplyInfoEntity::getPayApplyId, id));
        return entity.setInfoItems(infoItems);
    }

    @Override
    public String cast2PayApplyFromOrder(Date startTime, Date endTime, List<String> ids) {
        try {
            log.info("开始从订单数据转换为付款申请单，参数：startTime={}, endTime={}, ids={}", startTime, endTime, ids);

            // 查询符合条件的订单明细数据（排除换房相关订单）
            List<BilOrderItemWithOrderVo> orderItemsWithOrder = getValidOrderItemsForPayApply(startTime, endTime, ids);

            if (orderItemsWithOrder.isEmpty()) {
                return "未找到符合条件的订单明细数据";
            }

            log.info("找到{}条符合条件的订单明细数据", orderItemsWithOrder.size());

            // 从订单明细转换为付款申请单
            cast2PayApplyFromOrderItems(orderItemsWithOrder);

            // 统计转换结果
            long distinctOrderCount = orderItemsWithOrder.stream()
                .map(item -> item.getOrderId())
                .distinct()
                .count();

            log.info("成功处理{}个订单的{}条明细，转换为付款申请单", distinctOrderCount, orderItemsWithOrder.size());

            return "成功处理" + distinctOrderCount + "个订单的" + orderItemsWithOrder.size() + "条明细，转换为付款申请单";

        } catch (Exception e) {
            log.error("从订单数据转换为付款申请单失败", e);
            throw new RuntimeException("转换失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void cast2PayApplyFromBilOrders(Collection<BilOrderEntity> bilOrderEntities) {
        try {
            log.info("开始根据账单数据批量转换为付款申请单，数量：{}", bilOrderEntities.size());

            if (CollUtil.isEmpty(bilOrderEntities)) {
                log.warn("账单数据为空，跳过转换");
                return;
            }

            // 提取所有订单类型，用于获取收支项目映射
            Set<String> orderTypes = bilOrderEntities.stream()
                .map(BilOrderEntity::getOrderType)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 从INIT_MAP获取收支项目映射
            Map<String, String> expenseItemMap = Maps.newHashMap();
            CosmicExpenseItemServiceImpl.INIT_MAP.forEach((k, v) -> {
                if (orderTypes.contains(k)) {
                    expenseItemMap.put(k, v.getExpenseItemNumber());
                }
            });

            // 批量查询收支项目基础数据
            List<CosmicBaseDataEntity> cosmicBaseDataEntities = cosmicBaseDataService.listByTypeAndNumbers(
                CosmicBaseDataTypeEnum.EXPENSE_ITEM.getType(), expenseItemMap.values());
            Map<String, CosmicBaseDataEntity> expenseItemDataMap = cosmicBaseDataEntities.stream()
                .collect(Collectors.toMap(CosmicBaseDataEntity::getNumber, Function.identity()));

            // 提前批量获取所有账单的付款人ID
            Set<String> payerIds = bilOrderEntities.stream()
                .map(BilOrderEntity::getPayerId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

            // 批量查询所有银行账户信息
            final Map<String, BizAccountEntity> accountMap;
            final Map<String, CosmicBaseDataEntity> bankDataMap;
            
            if (CollUtil.isNotEmpty(payerIds)) {
                QueryWrapper<BizAccountEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("renter_id", payerIds);
                List<BizAccountEntity> accountEntities = bizAccountDao.selectList(queryWrapper);
                accountMap = accountEntities.stream()
                    .collect(Collectors.toMap(BizAccountEntity::getRenterId, Function.identity(), (v1, v2) -> v1));

                // 收集所有需要查询的银行编号
                Set<String> bankBranchCodes = accountEntities.stream()
                    .map(BizAccountEntity::getBankBranch)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());

                // 批量查询所有银行基础数据
                List<CosmicBaseDataEntity> bankDataEntities = cosmicBaseDataService.listByTypeAndName(
                    CosmicBaseDataTypeEnum.BANK.getType(), bankBranchCodes);
                Map<String, CosmicBaseDataEntity> bankMap = bankDataEntities.stream()
                    .collect(Collectors.toMap(CosmicBaseDataEntity::getName, Function.identity(), (v1, v2) -> v1));

                // 将银行信息与账户ID关联
                Map<String, CosmicBaseDataEntity> tempBankDataMap = Maps.newHashMap();
                for (BizAccountEntity account : accountEntities) {
                    if (StrUtil.isNotBlank(account.getBankBranch()) && bankMap.containsKey(account.getBankBranch())) {
                        tempBankDataMap.put(account.getRenterId(), bankMap.get(account.getBankBranch()));
                    }
                }
                bankDataMap = tempBankDataMap;
            } else {
                accountMap = Maps.newHashMap();
                bankDataMap = Maps.newHashMap();
            }

            // 转换账单为付款申请单
            List<CosmicPayApplyEntity> payApplyEntities = bilOrderEntities.stream()
                .map(bilOrder -> convertBilOrderToPayApply(bilOrder, expenseItemMap, expenseItemDataMap, accountMap, bankDataMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (payApplyEntities.isEmpty()) {
                log.warn("没有生成有效的付款申请单数据");
                return;
            }

            // 批量保存付款申请单
            saveBatchWithItem(payApplyEntities);

            log.info("成功转换{}条账单为付款申请单", payApplyEntities.size());

        } catch (Exception e) {
            log.error("根据账单数据转换为付款申请单失败", e);
            throw new RuntimeException("转换失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将单个账单转换为付款申请单
     *
     * @param bilOrder 账单实体
     * @param expenseItemMap 收支项目映射
     * @param expenseItemDataMap 收支项目基础数据映射
     * @param accountMap 银行账户映射
     * @param bankDataMap 银行基础数据映射
     * @return 付款申请单实体
     */
    private CosmicPayApplyEntity convertBilOrderToPayApply(BilOrderEntity bilOrder,
                                                          Map<String, String> expenseItemMap,
                                                          Map<String, CosmicBaseDataEntity> expenseItemDataMap,
                                                          Map<String, BizAccountEntity> accountMap,
                                                          Map<String, CosmicBaseDataEntity> bankDataMap) {
        if (bilOrder == null || bilOrder.getPayment() == null || bilOrder.getPayment().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        // 创建付款申请单主表
        CosmicPayApplyEntity payApply = new CosmicPayApplyEntity();
        
        // 设置基本信息
        payApply.setSourceBillNumber(bilOrder.getId())
                .setSourceBillType(bilOrder.getOrderType())
                .setSourceSystem("1") // 系统来源
                .setApplyDate(bilOrder.getCreateDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate())
                .setPayOrgNumber(CosmicConstant.ORG_NUMBER)
                .setApplyOrgNumber(CosmicConstant.ORG_NUMBER)
                .setCreatorNumber(CosmicConstant.CREATOR_NUMBER)
                .setPaymentIdentifyNumber("FKBS01")
                .setPayeeAmount(bilOrder.getPayment().abs()) // 使用绝对值
                .setPaidStatus(convertPayStateToPaidStatus(bilOrder.getPayState()));

        // 根据收支项目确定付款类型
        String expenseItemNumber = expenseItemMap.get(bilOrder.getOrderType());
        CosmicBaseDataEntity expenseItemData = expenseItemDataMap.get(expenseItemNumber);
        if (expenseItemData != null) {
            JSONObject expandData = JSONObject.from(expenseItemData.getExpandDataJson());
            boolean isSettlement = expandData.getBooleanValue("issettlement", true);
            // 根据收支项目是否参与结算确定付款类型
            if (isSettlement) {
                payApply.setPaymentTypeNumber("02"); // 付款
            } else {
                payApply.setPaymentTypeNumber("211"); // 费用报销
            }
        } else {
            payApply.setPaymentTypeNumber("02"); // 默认付款
        }

        // 填充申请明细分录（InfoItems）
        fillPayApplyInfoItems(payApply, bilOrder, accountMap, bankDataMap);

        // 填充业务明细分录（BusinessItems）
        fillPayApplyBusinessItems(payApply, bilOrder, expenseItemData);

        return payApply;
    }

    /**
     * 将单个订单的多个明细转换为一个付款申请单
     *
     * @param orderId 订单ID
     * @param orderItems 订单明细列表
     * @param expenseItemMap 收支项目映射
     * @param expenseItemDataMap 收支项目基础数据映射
     * @param accountMap 银行账户映射
     * @param bankDataMap 银行基础数据映射
     * @return 付款申请单实体
     */
    private CosmicPayApplyEntity convertOrderItemsToPayApply(String orderId,
                                                           List<BilOrderItemWithOrderVo> orderItems,
                                                           Map<String, String> expenseItemMap,
                                                           Map<String, CosmicBaseDataEntity> expenseItemDataMap,
                                                           Map<String, BizAccountEntity> accountMap,
                                                           Map<String, CosmicBaseDataEntity> bankDataMap) {
        if (CollUtil.isEmpty(orderItems)) {
            return null;
        }

        // 获取第一个明细的订单信息（同一订单的明细共享订单信息）
        BilOrderItemWithOrderVo firstItem = orderItems.get(0);
        BilOrderVo bilOrder = firstItem.getBilOrderVo();
        bilOrder.setCreateDate(firstItem.getCreateDate());
        if (bilOrder == null) {
            log.warn("订单{}的明细缺少主订单信息，跳过转换", orderId);
            return null;
        }

        // 计算订单总的付款金额（所有有效明细取反后的金额之和）
        BigDecimal totalPayeeAmount = orderItems.stream()
            .map(BilOrderItemWithOrderVo::getPayment)
            .filter(Objects::nonNull)
            .map(BigDecimal::negate) // 先取反
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .abs(); // 然后取绝对值，确保付款单总金额为正数

        if (totalPayeeAmount.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("订单{}的总付款金额为0，跳过转换", orderId);
            return null;
        }

        // 创建付款申请单主表
        CosmicPayApplyEntity payApply = new CosmicPayApplyEntity();
        
        // 设置基本信息
        payApply.setSourceBillNumber(orderId)
                .setSourceBillType(bilOrder.getOrderType())
                .setSourceSystem("DMK") // 系统来源
                .setApplyDate(bilOrder.getCreateDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate())
                .setPayOrgNumber(CosmicConstant.ORG_NUMBER)
                .setApplyOrgNumber(CosmicConstant.ORG_NUMBER)
                .setCreatorNumber(CosmicConstant.CREATOR_NUMBER)
                .setPaymentIdentifyNumber("FKBS01")
                .setPayeeAmount(totalPayeeAmount)
                .setPaidStatus(convertPayStateToPaidStatus(bilOrder.getPayState()));

        // 根据明细项确定付款类型（使用第一个明细的收支项目）
        String firstItemType = firstItem.getOrderItemType();
        String expenseItemNumber = expenseItemMap.get(firstItemType);
        CosmicBaseDataEntity expenseItemData = expenseItemDataMap.get(expenseItemNumber);
        if (expenseItemData != null) {
            JSONObject expandData = JSONObject.from(expenseItemData.getExpandDataJson());
            boolean isSettlement = expandData.getBooleanValue("issettlement", true);
            // 根据收支项目是否参与结算确定付款类型
            if (isSettlement) {
                payApply.setPaymentTypeNumber("02"); // 付款
            } else {
                payApply.setPaymentTypeNumber("211"); // 费用报销
            }
        } else {
            payApply.setPaymentTypeNumber("02"); // 默认付款
        }

        // 填充申请明细分录（InfoItems）- 按订单维度，一个订单一个Info记录
        fillPayApplyInfoItemsFromOrder(payApply, bilOrder, accountMap, bankDataMap);

        // 填充业务明细分录（BusinessItems）- 按明细维度，每个明细生成一个Business记录
        fillPayApplyBusinessItemsFromOrderItems(payApply, orderItems, expenseItemMap, expenseItemDataMap);

        return payApply;
    }

    /**
     * 基于订单信息填充付款申请单申请明细分录
     */
    private void fillPayApplyInfoItemsFromOrder(CosmicPayApplyEntity payApply, BilOrderVo bilOrder,
                                              Map<String, BizAccountEntity> accountMap,
                                              Map<String, CosmicBaseDataEntity> bankDataMap) {
        CosmicPayApplyInfoEntity info = new CosmicPayApplyInfoEntity();
        
        // 设置基本信息
        info.setSettlementTypeNumber("JSFS13") // 结算方式：转账
            .setPayeeAmount(payApply.getPayeeAmount())
            .setPriority("public") // 紧急程度：普通
            .setPayeeType("other"); // 收款人类型：其他

        // 获取收款人银行账户信息
        String payerId = bilOrder.getPayerId();
        if (StrUtil.isNotBlank(payerId) && accountMap.containsKey(payerId)) {
            BizAccountEntity accountEntity = accountMap.get(payerId);
            if (accountEntity != null) {
                info.setPayeeAccBankNum(accountEntity.getCode()) // 收款账号
                    .setPayeeName(accountEntity.getName()) // 收款人姓名
                    .setAccountName(accountEntity.getName()); // 账户名称

                // 设置收款银行信息
                CosmicBaseDataEntity bankData = bankDataMap.get(payerId);
                if (bankData != null) {
                    info.setPayeeBankNumber(bankData.getNumber());
                } else {
                    // 如果没有金蝶映射，使用原始支行信息
                    info.setRemark("银行：" + accountEntity.getBankBranch());
                }
            }
        }

        payApply.setInfoItems(ListUtil.toList(info));
    }

    /**
     * 基于订单明细列表填充付款申请单业务明细分录
     * 
     * 此方法的主要作用：
     * 1. 遍历订单明细列表，为每个明细创建一个业务分录记录
     * 2. 设置业务分录的金额（取反处理：负数变正数，正数变负数）
     * 3. 根据明细类型匹配对应的收支项目编号
     * 4. 根据收支项目的扩展数据配置，动态设置项目编号、合同编号、业态类型等字段
     * 5. 最终将所有业务分录记录设置到付款申请单中
     * 
     * @param payApply 付款申请单实体对象
     * @param orderItems 订单明细列表（包含订单信息的明细对象）
     * @param expenseItemMap 明细类型与收支项目编号的映射关系
     * @param expenseItemDataMap 收支项目编号与基础数据实体的映射关系
     */
    private void fillPayApplyBusinessItemsFromOrderItems(CosmicPayApplyEntity payApply, 
                                                       List<BilOrderItemWithOrderVo> orderItems,
                                                       Map<String, String> expenseItemMap,
                                                       Map<String, CosmicBaseDataEntity> expenseItemDataMap) {
        List<CosmicPayApplyBusinessEntity> businessItems = Lists.newArrayList();
        
        // 遍历每个订单明细，为每个明细创建一个业务分录记录
        for (BilOrderItemWithOrderVo orderItem : orderItems) {
            CosmicPayApplyBusinessEntity business = new CosmicPayApplyBusinessEntity();
            
            // 设置基本业务信息 - 取反金额（负数变正数，正数变负数）
            // 这里取反是因为业务需求：订单明细的金额与付款申请的金额方向相反
            business.setBusPayeeAmount(orderItem.getPayment().negate()); // 金额取反
            
            // 根据明细类型获取对应的收支项目编号
            String expenseItemNumber = expenseItemMap.get(orderItem.getOrderItemType());
            CosmicBaseDataEntity expenseItemData = expenseItemDataMap.get(expenseItemNumber);
            
            if (expenseItemData != null) {
                // 设置收支项目编号
                business.setExpenseItemNumber(expenseItemData.getNumber());

                // 处理扩展数据，根据收支项目配置动态设置相关字段
                JSONObject expandData = JSONObject.from(expenseItemData.getExpandDataJson());
                List<JSONObject> assList = MapUtil.get(expandData, "assList", new TypeReference<List<JSONObject>>() {});

                if (assList != null) {
                    // 获取明细的关联信息（项目编码和平台信息）
                    Map<String, Object> orderItemInfo = getOrderItemRelatedInfo(orderItem);
                    String projectCode = (String) orderItemInfo.get("projectCode");
                    String platform = (String) orderItemInfo.get("platform");

                    // 遍历收支项目的辅助配置列表，根据配置类型设置对应字段
                    for (JSONObject jsonObject : assList) {
                        String valueTypeName = jsonObject.getOrDefault("valueTypeName", "").toString();

                        // 处理基础资料类型的配置
                        if ("基础资料".equals(valueTypeName)) {
                            if (CosmicBaseDataTypeEnum.PROJECT.getDesc().equals(jsonObject.getOrDefault("name", ""))) {
                                // 如果配置了项目维度，根据项目编码查表获取对应的项目编号
                                if (StrUtil.isNotBlank(projectCode)) {
                                    String projectNumber = queryProjectNumberByCode(projectCode);
                                    if (StrUtil.isNotBlank(projectNumber)) {
                                        business.setProjectNumber(projectNumber);
                                    } else {
                                        log.warn("未找到项目编码{}对应的项目编号", projectCode);
                                    }
                                }
                            }
                        }

                        // 处理辅助资料类型的配置
                        if ("辅助资料".equals(valueTypeName)) {
                            AssistantDataTypeEnum assistantDataTypeEnum =
                                AssistantDataTypeEnum.parseEnumByName(jsonObject.getOrDefault("name", "").toString());
                            if (assistantDataTypeEnum != null) {
                                switch (assistantDataTypeEnum) {
                                    case CONTRACT:
                                        // 如果配置了合同维度，设置固定的合同编号
                                        business.setContractNumber("GDJTHT-GGHT"); // 固定合同号
                                        break;
                                    case BIZ_TYPE:
                                        // 如果配置了业态类型维度，根据平台信息设置对应的业态类型编号
                                        if (StrUtil.isNotBlank(platform)) {
                                            business.setBizTypeNumber(CosmicConstant.SOCIAL_CONTRACT_BIZ_TYPE_NUMBER);
                                            // if ("0".equals(platform)) {
                                            //     // 平台0：社会合同
                                            //     business.setBizTypeNumber(CosmicConstant.SOCIAL_CONTRACT_BIZ_TYPE_NUMBER);
                                            // } else {
                                            //     // 其他平台：员工宿舍合同
                                            //     business.setBizTypeNumber(CosmicConstant.EMPLOYEE_DORMITORY_CONTRACT_BIZ_TYPE_NUMBER);
                                            // }
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
            }

            String remarkInfo = getRemarkInfo(payApply, orderItem);


            // 设置备注信息
            business.setRemark(remarkInfo);

            // 将创建的业务分录添加到列表中
            businessItems.add(business);
        }
        
        // 将所有业务分录设置到付款申请单中
        payApply.setBusinessItems(businessItems);
    }


    /**
     * 组装摘要信息
     *
     * @param payApply payApply
     * @param orderItem orderItem
     * @return NotNullString
     */
    private static @NotNull String getRemarkInfo(CosmicPayApplyEntity payApply, BilOrderItemWithOrderVo orderItem) {
        // 退【滨海里2号及5号公寓**房间号**租户】【物业费】【2025-05-15】至【2025-05-31】
        
        // 获取地址信息
        String address = "";
        if (orderItem != null && orderItem.getBilOrderVo() != null) {
            address = StrUtil.blankToDefault(orderItem.getBilOrderVo().getAddress(), "");
        }

        // 获取租户信息
        String payeeName = "";
        if (payApply != null && CollUtil.isNotEmpty(payApply.getInfoItems())) {
            CosmicPayApplyInfoEntity firstInfo = payApply.getInfoItems().get(0);
            if (firstInfo != null) {
                payeeName = StrUtil.blankToDefault(firstInfo.getPayeeName(), "");
            }
        }

        // 获取账单周期
        Date startDate = null;
        Date endDate = null;
        if (orderItem != null && orderItem.getBilOrderVo() != null) {
            startDate = orderItem.getStartTime();
            endDate = orderItem.getEndTime();
        }

        // 获取类型映射
        String orderItemType = "";
        if (orderItem != null && StrUtil.isNotBlank(orderItem.getOrderItemType())) {
            orderItemType = StrUtil.blankToDefault(
                OrderItemTypeEnum.getNameByValue(orderItem.getOrderItemType()), 
                orderItem.getOrderItemType()
            );
        }

        // 组装备注信息：退【滨海里2号及5号公寓**房间号**租户】【物业费】【2025-05-15】至【2025-05-31】
        StringBuilder remarkBuilder = new StringBuilder();
        remarkBuilder.append("退【");

        // 添加地址信息
        if (StrUtil.isNotBlank(address)) {
            remarkBuilder.append(address);
        }

        // 添加租户信息
        if (StrUtil.isNotBlank(payeeName)) {
            remarkBuilder.append(payeeName);
        }

        remarkBuilder.append("】【");

        // 添加费用类型
        if (StrUtil.isNotBlank(orderItemType)) {
            remarkBuilder.append(orderItemType);
        }

        remarkBuilder.append("】");

        // 只有当开始时间和结束时间都不为空时，才拼接日期部分
        if (startDate != null && endDate != null) {
            remarkBuilder.append("【");
            remarkBuilder.append(DateUtil.formatDate(startDate));
            remarkBuilder.append("】至【");
            remarkBuilder.append(DateUtil.formatDate(endDate));
            remarkBuilder.append("】");
        }

        String remarkInfo = remarkBuilder.toString();
        
        // 验证生成的备注信息长度，避免过长导致数据库字段溢出
        if (remarkInfo.length() > 200) { // 假设数据库字段长度限制为200
            log.warn("生成的备注信息过长：{}，将被截断", remarkInfo);
            remarkInfo = remarkInfo.substring(0, 197) + "...";
        }
        
        return remarkInfo;
    }

    /**
     * 获取订单明细的关联信息（项目编码和平台信息）
     */
    private Map<String, Object> getOrderItemRelatedInfo(BilOrderItemWithOrderVo orderItem) {
        Map<String, Object> result = Maps.newHashMap();
        
        // 从订单明细的关联订单信息中获取真实的项目编码和平台信息
        BilOrderVo bilOrder = orderItem.getBilOrderVo();
        if (bilOrder != null) {
            // 获取真实的项目编码
            String projectCode = bilOrder.getProjectCode();
            if (StrUtil.isNotBlank(projectCode)) {
                result.put("projectCode", projectCode);
            } else {
                result.put("projectCode", "AMBH"); // 默认项目编码
                log.warn("订单{}的项目编码为空，使用默认值AMBH", orderItem.getOrderId());
            }
            
            // 获取真实的平台信息
            String platform = bilOrder.getPlatform();
            if (StrUtil.isNotBlank(platform)) {
                result.put("platform", platform);
            } else {
                result.put("platform", "0"); // 默认平台：社会合同
                log.warn("订单{}的平台信息为空，使用默认值0（社会合同）", orderItem.getOrderId());
            }
        } else {
            // 如果订单信息为空，使用默认值并记录警告
            result.put("projectCode", "AMBH");
            result.put("platform", "0");
            log.warn("订单明细{}缺少关联的订单信息，使用默认值：projectCode=AMBH, platform=0", 
                orderItem.getId());
        }
        
        log.debug("订单{}的关联信息：projectCode={}, platform={}", 
            orderItem.getOrderId(), result.get("projectCode"), result.get("platform"));
        
        return result;
    }

    /**
     * 根据项目编码查询对应的项目编号
     * 
     * @param projectCode 项目编码
     * @return 项目编号
     */
    private String queryProjectNumberByCode(String projectCode) {
        if (StrUtil.isBlank(projectCode)) {
            return null;
        }
        
        try {
            // 查询金蝶基础数据中的项目编号
            List<CosmicBaseDataEntity> projectData = cosmicBaseDataService.listByTypeAndName(
                CosmicBaseDataTypeEnum.PROJECT.getType(), Lists.newArrayList(projectCode));
            
            if (CollUtil.isNotEmpty(projectData)) {
                CosmicBaseDataEntity projectEntity = projectData.get(0);
                String projectNumber = projectEntity.getNumber();
                log.debug("项目编码{}对应的项目编号：{}", projectCode, projectNumber);
                return projectNumber;
            } else {
                // 如果查不到，使用原有的硬编码逻辑作为兜底
                if ("AMBH".equals(projectCode)) {
                    log.info("项目编码{}未在基础数据中找到，使用兜底常量{}", projectCode, CosmicConstant.PROJECT_NUMBER_AMBH);
                    return CosmicConstant.PROJECT_NUMBER_AMBH;
                } else if ("AMYN".equals(projectCode)) {
                    log.info("项目编码{}未在基础数据中找到，使用兜底常量{}", projectCode, CosmicConstant.PROJECT_NUMBER_AMYN);
                    return CosmicConstant.PROJECT_NUMBER_AMYN;
                } else {
                    log.warn("未找到项目编码{}的映射关系，既不在基础数据中，也不在兜底常量中", projectCode);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("查询项目编码{}对应的项目编号时发生异常", projectCode, e);
            // 异常情况下使用兜底逻辑
            if ("AMBH".equals(projectCode)) {
                return CosmicConstant.PROJECT_NUMBER_AMBH;
            } else if ("AMYN".equals(projectCode)) {
                return CosmicConstant.PROJECT_NUMBER_AMYN;
            }
            return null;
        }
    }

    /**
     * 填充付款申请单申请明细分录
     */
    private void fillPayApplyInfoItems(CosmicPayApplyEntity payApply, BilOrderEntity bilOrder,
                                      Map<String, BizAccountEntity> accountMap,
                                      Map<String, CosmicBaseDataEntity> bankDataMap) {
        CosmicPayApplyInfoEntity info = new CosmicPayApplyInfoEntity();
        
        // 设置基本信息
        info.setSettlementTypeNumber("JSFS13") // 结算方式：转账
            .setPayeeAmount(payApply.getPayeeAmount())
            .setPriority("public") // 紧急程度：普通
            .setPayeeType("other"); // 收款人类型：其他

        // 获取收款人银行账户信息
        String payerId = bilOrder.getPayerId();
        if (StrUtil.isNotBlank(payerId) && accountMap.containsKey(payerId)) {
            BizAccountEntity accountEntity = accountMap.get(payerId);
            if (accountEntity != null) {
                info.setPayeeAccBankNum(accountEntity.getCode()) // 收款账号
                    .setPayeeName(accountEntity.getName()) // 收款人姓名
                    .setAccountName(accountEntity.getName()); // 账户名称

                // 设置收款银行信息
                CosmicBaseDataEntity bankData = bankDataMap.get(payerId);
                if (bankData != null) {
                    info.setPayeeBankNumber(bankData.getNumber());
                } else {
                    // 如果没有金蝶映射，使用原始支行信息
                    info.setRemark("银行：" + accountEntity.getBankBranch());
                }
            }
        }

        payApply.setInfoItems(ListUtil.toList(info));
    }

    /**
     * 填充付款申请单业务明细分录
     */
    private void fillPayApplyBusinessItems(CosmicPayApplyEntity payApply, BilOrderEntity bilOrder,
                                          CosmicBaseDataEntity expenseItemData) {
        CosmicPayApplyBusinessEntity business = new CosmicPayApplyBusinessEntity();
        
        // 设置基本业务信息
        business.setBusPayeeAmount(payApply.getPayeeAmount());
        
        if (expenseItemData != null) {
            business.setExpenseItemNumber(expenseItemData.getNumber());
            
            // 处理扩展数据，设置项目、合同等信息
            JSONObject expandData = JSONObject.from(expenseItemData.getExpandDataJson());
            List<JSONObject> assList = MapUtil.get(expandData, "assList", new TypeReference<List<JSONObject>>() {});
            
            if (assList != null) {
                // 获取账单的关联信息（项目编码和平台信息）
                Map<String, Object> bilOrderInfo = getBilOrderRelatedInfo(bilOrder.getId());
                String projectCode = (String) bilOrderInfo.get("projectCode");
                String platform = (String) bilOrderInfo.get("platform");
                
                for (JSONObject jsonObject : assList) {
                    String valueTypeName = jsonObject.getOrDefault("valueTypeName", "").toString();
                    
                    if ("基础资料".equals(valueTypeName)) {
                        if (CosmicBaseDataTypeEnum.PROJECT.getDesc().equals(jsonObject.getOrDefault("name", ""))) {
                            // 根据项目编码设置项目编号
                            if (StrUtil.isNotBlank(projectCode)) {
                                if ("AMBH".equals(projectCode)) {
                                    business.setProjectNumber(CosmicConstant.PROJECT_NUMBER_AMBH);
                                } else if ("AMYN".equals(projectCode)) {
                                    business.setProjectNumber(CosmicConstant.PROJECT_NUMBER_AMYN);
                                }
                            }
                        }
                    }
                    
                    if ("辅助资料".equals(valueTypeName)) {
                        AssistantDataTypeEnum assistantDataTypeEnum = 
                            AssistantDataTypeEnum.parseEnumByName(jsonObject.getOrDefault("name", "").toString());
                        if (assistantDataTypeEnum != null) {
                            switch (assistantDataTypeEnum) {
                                case CONTRACT:
                                    business.setContractNumber(CosmicConstant.CONTRACT_NUMBER);
                                    break;
                                case BIZ_TYPE:
                                    // 根据平台信息设置业态类型编号
                                    if (StrUtil.isNotBlank(platform)) {
                                        if ("0".equals(platform)) {
                                            business.setBizTypeNumber(CosmicConstant.SOCIAL_CONTRACT_BIZ_TYPE_NUMBER);
                                        } else {
                                            business.setBizTypeNumber(CosmicConstant.EMPLOYEE_DORMITORY_CONTRACT_BIZ_TYPE_NUMBER);
                                        }
                                    }
                                    break;
                            }
                        }
                    }
                }
            }
        }
        
        payApply.setBusinessItems(ListUtil.toList(business));
    }
    
    /**
     * 获取账单的关联信息（项目编码和平台信息）
     * TODO: 这里暂时使用默认值，实际项目中需要根据账单的房源ID和合同ID查询真实的项目编码和平台信息
     */
    private Map<String, Object> getBilOrderRelatedInfo(String bilOrderId) {
        Map<String, Object> result = Maps.newHashMap();
        
        // 暂时设置默认值，确保逻辑正确性
        // 在实际使用时，需要根据账单的sourceId查询项目编码，根据contractId查询平台信息
        result.put("projectCode", "AMBH"); // 默认项目编码，可根据实际情况调整
        result.put("platform", "0");       // 默认平台，0代表社会合同
        
        // TODO: 实际实现时可能需要：
        // 1. 根据bilOrderId获取账单信息
        // 2. 根据账单的sourceId查询房源表，再查询项目表获取projectCode  
        // 3. 根据账单的contractId查询合同表获取platform
        // 4. 类似cast2PayApplySingle方法中的实现方式
        
        return result;
    }

    /**
     * 将账单支付状态转换为付款申请单支付状态
     */
    private String convertPayStateToPaidStatus(String payState) {
        if (StrUtil.isBlank(payState)) {
            return PaidStatusEnum.NO_PAID.getValue();
        }
        
        PayStateEnum payStateEnum = PayStateEnum.findPayStatusEnum(payState);
        if (payStateEnum != null) {
            switch (payStateEnum) {
                case NOPAY:
                case REFUNDPENDING:
                    return PaidStatusEnum.NO_PAID.getValue();
                case PAYCONFIR:
                case REFUNDED:
                    return PaidStatusEnum.PAID.getValue();
                case REFUNDTOAUDIT:
                case REFUNDAUDITING:
                    return PaidStatusEnum.PAYING.getValue();
                default:
                    return PaidStatusEnum.NO_PAID.getValue();
            }
        }
        
        return PaidStatusEnum.NO_PAID.getValue();
    }

    /**
     * 查询有效的订单明细数据，排除换房相关订单和无效明细
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ids 指定的订单ID列表
     * @return 有效的订单明细列表
     */
    private List<BilOrderItemWithOrderVo> getValidOrderItemsForPayApply(Date startTime, Date endTime, List<String> ids) {
        log.info("开始查询有效的订单明细数据，排除换房相关订单");
        
        // 优化查询流程：直接调用专门的查询方法，在数据库层面过滤订单类型和明细条件
        List<BilOrderItemWithOrderVo> filteredItems = getOrderItemsWithOrderTypeFilter(startTime, endTime, ids);
        
        if (CollUtil.isEmpty(filteredItems)) {
            log.warn("未查询到符合条件的订单明细数据（订单类型：140，payment!=0）");
            return Lists.newArrayList();
        }
        
        log.info("数据库层过滤后获得{}条有效明细数据", filteredItems.size());
        
        // 排除换房相关的订单
        List<BilOrderItemWithOrderVo> nonChangeRoomItems = excludeChangeRoomOrders(filteredItems);
        
        log.info("查询优化完成，最终有效明细数量：{}，数据库层过滤减少了应用层处理开销", 
            nonChangeRoomItems.size());
        
        return nonChangeRoomItems;
    }

    /**
     * 在数据库层面优化查询，直接过滤订单类型和明细条件
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ids 指定的订单ID列表
     * @return 已过滤的订单明细列表
     */
    private List<BilOrderItemWithOrderVo> getOrderItemsWithOrderTypeFilter(Date startTime, Date endTime, List<String> ids) {
        // 使用原有的查询方法，但增加数据库层面的优化
        List<BilOrderItemWithOrderVo> allItems = bilOrderItemDao.getItemWithOrderVoList(
            startTime, endTime, null, 0, null, "PAYMENT", ids);
        
        if (CollUtil.isEmpty(allItems)) {
            return Lists.newArrayList();
        }
        
        // 优化过滤逻辑：一次性过滤所有条件，减少Stream操作次数
        List<BilOrderItemWithOrderVo> validItems = allItems.stream()
            .filter(item -> {
                // 条件1：检查订单信息是否存在且类型为140
                BilOrderVo bilOrder = item.getBilOrderVo();
                if (bilOrder == null || !"140".equals(bilOrder.getOrderType())) {
                    return false;
                }
                
                // 条件2：检查明细金额是否有效
                BigDecimal payment = item.getPayment();
                if (payment == null || payment.compareTo(BigDecimal.ZERO) == 0) {
                    return false;
                }
                
                return true;
            })
            .collect(Collectors.toList());
        
        log.info("应用层过滤完成，原始数据：{}条，有效数据：{}条，过滤率：{}%", 
            allItems.size(), validItems.size(), 
            allItems.size() > 0 ? Math.round((1.0 - (double)validItems.size() / allItems.size()) * 100) : 0);
        
        return validItems;
    }

    /**
     * 排除换房相关的订单明细
     * 
     * @param orderItems 原始订单明细列表
     * @return 排除换房后的订单明细列表
     */
    private List<BilOrderItemWithOrderVo> excludeChangeRoomOrders(List<BilOrderItemWithOrderVo> orderItems) {
        if (CollUtil.isEmpty(orderItems)) {
            return Lists.newArrayList();
        }
        
        // 提取所有订单ID
        Set<String> orderIds = orderItems.stream()
            .map(BilOrderItemWithOrderVo::getOrderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        if (orderIds.isEmpty()) {
            return Lists.newArrayList();
        }
        
        // 查询这些订单中哪些参与了换房结转
        QueryWrapper<BilCarryOverEntity> carryOverQuery = new QueryWrapper<>();
        carryOverQuery.in("from_order_id", orderIds);
        List<BilCarryOverEntity> carryOverRecords = bilCarryOverService.list(carryOverQuery);
        
        Set<String> changeRoomOrderIds = carryOverRecords.stream()
            .map(BilCarryOverEntity::getFromOrderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        if (!changeRoomOrderIds.isEmpty()) {
            log.info("发现{}个换房相关订单，将被排除：{}", changeRoomOrderIds.size(), changeRoomOrderIds);
        }
        
        // 过滤掉换房相关的订单明细
        List<BilOrderItemWithOrderVo> result = orderItems.stream()
            .filter(item -> !changeRoomOrderIds.contains(item.getOrderId()))
            .collect(Collectors.toList());
        
        log.info("成功排除换房订单，剩余有效明细数量：{}", result.size());
        
        return result;
    }

    /**
     * 从订单明细转换为付款申请单
     * 
     * @param orderItemsWithOrder 订单明细列表
     */
    @Transactional
    public void cast2PayApplyFromOrderItems(List<BilOrderItemWithOrderVo> orderItemsWithOrder) {
        try {
            log.info("开始根据订单明细数据批量转换为付款申请单，明细数量：{}", orderItemsWithOrder.size());

            if (CollUtil.isEmpty(orderItemsWithOrder)) {
                log.warn("订单明细数据为空，跳过转换");
                return;
            }

            // 按订单ID分组处理明细
            Map<String, List<BilOrderItemWithOrderVo>> orderItemsMap = orderItemsWithOrder.stream()
                .collect(Collectors.groupingBy(BilOrderItemWithOrderVo::getOrderId));

            log.info("按订单分组后，共有{}个订单需要处理", orderItemsMap.size());

            // 提取所有订单明细类型，用于获取收支项目映射
            Set<String> orderItemTypes = orderItemsWithOrder.stream()
                .map(BilOrderItemWithOrderVo::getOrderItemType)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 从INIT_MAP获取收支项目映射（基于明细类型而不是订单类型）
            Map<String, String> expenseItemMap = Maps.newHashMap();
            CosmicExpenseItemServiceImpl.INIT_MAP.forEach((k, v) -> {
                if (orderItemTypes.contains(k)) {
                    expenseItemMap.put(k, v.getExpenseItemNumber());
                }
            });

            // 批量查询收支项目基础数据
            List<CosmicBaseDataEntity> cosmicBaseDataEntities = cosmicBaseDataService.listByTypeAndNumbers(
                CosmicBaseDataTypeEnum.EXPENSE_ITEM.getType(), expenseItemMap.values());
            Map<String, CosmicBaseDataEntity> expenseItemDataMap = cosmicBaseDataEntities.stream()
                .collect(Collectors.toMap(CosmicBaseDataEntity::getNumber, Function.identity()));

            // 提前批量获取所有订单的付款人ID
            Set<String> payerIds = orderItemsWithOrder.stream()
                .map(item -> item.getBilOrderVo() != null ? item.getBilOrderVo().getPayerId() : null)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

            // 批量查询银行账户信息
            final Map<String, BizAccountEntity> accountMap;
            final Map<String, CosmicBaseDataEntity> bankDataMap;
            
            if (CollUtil.isNotEmpty(payerIds)) {
                QueryWrapper<BizAccountEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("renter_id", payerIds);
                List<BizAccountEntity> accountEntities = bizAccountDao.selectList(queryWrapper);
                accountMap = accountEntities.stream()
                    .collect(Collectors.toMap(BizAccountEntity::getRenterId, Function.identity(), (v1, v2) -> v1));

                // 收集所有需要查询的银行编号
                Set<String> bankBranchCodes = accountEntities.stream()
                    .map(BizAccountEntity::getBankBranch)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());

                // 批量查询所有银行基础数据
                List<CosmicBaseDataEntity> bankDataEntities = cosmicBaseDataService.listByTypeAndName(
                    CosmicBaseDataTypeEnum.BANK.getType(), bankBranchCodes);
                Map<String, CosmicBaseDataEntity> bankMap = bankDataEntities.stream()
                    .collect(Collectors.toMap(CosmicBaseDataEntity::getName, Function.identity(), (v1, v2) -> v1));

                // 将银行信息与账户ID关联
                Map<String, CosmicBaseDataEntity> tempBankDataMap = Maps.newHashMap();
                for (BizAccountEntity account : accountEntities) {
                    if (StrUtil.isNotBlank(account.getBankBranch()) && bankMap.containsKey(account.getBankBranch())) {
                        tempBankDataMap.put(account.getRenterId(), bankMap.get(account.getBankBranch()));
                    }
                }
                bankDataMap = tempBankDataMap;
            } else {
                accountMap = Maps.newHashMap();
                bankDataMap = Maps.newHashMap();
            }

            // 为每个订单生成付款申请单
            List<CosmicPayApplyEntity> payApplyEntities = orderItemsMap.entrySet().stream()
                .map(entry -> convertOrderItemsToPayApply(entry.getKey(), entry.getValue(), 
                    expenseItemMap, expenseItemDataMap, accountMap, bankDataMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (payApplyEntities.isEmpty()) {
                log.warn("没有生成有效的付款申请单数据");
                return;
            }

            // 批量保存付款申请单
            saveBatchWithItem(payApplyEntities);

            log.info("成功转换{}个订单为付款申请单", payApplyEntities.size());

        } catch (Exception e) {
            log.error("根据订单明细数据转换为付款申请单失败", e);
            throw new RuntimeException("转换失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将账单数据转换为应付单格式，便于复用现有转换逻辑
     * @deprecated 此方法已废弃，使用直接转换方式
     * @param bilOrder 账单实体
     * @return 应付单实体
     */
    @Deprecated
    private CosmicAccountsPayableEntity createAccountsPayableFromBilOrder(BilOrderEntity bilOrder) {
        CosmicAccountsPayableEntity apFinOtherBtS = new CosmicAccountsPayableEntity()
            .setSourceBillNumber(bilOrder.getId()).setSourceBillType(bilOrder.getOrderType())
            .setBizDate(bilOrder.getCreateDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate())
            .setPriceTaxTotal(bilOrder.getPayment()).setIsPush(0).setOrgId(CosmicConstant.ORG_NUMBER)
            .setPayOrgId(CosmicConstant.ORG_NUMBER).setBillTypeNumber("ApFin_other_BT_S");

        return apFinOtherBtS;
    }

}
