package cn.uone.business.bil.controller;


import cn.uone.bean.entity.business.bil.BilLiveErrorLogEntity;
import cn.uone.bean.parameter.LiveErrorLogPo;
import cn.uone.business.bil.dao.BilLiveErrorLogDao;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
@RestController
@RequestMapping("/live/log")
@Api(value="跑批失败账单服务",tags={"跑批失败账单服务"})
public class BilLiveErrorLogController extends BaseController {

    @Resource
    private BilLiveErrorLogDao logDao;

    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/page")
    @ApiOperation("查询分页")
    public RestResponse page(Page page, LiveErrorLogPo po) {
        po.setProjectId(UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(logDao.getList(page,po));
    }

    @RequestMapping(value = {"/export"}, method = RequestMethod.POST)
    @UonePermissions
    public void export(HttpServletResponse response, LiveErrorLogPo po) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        po.setProjectId(UoneHeaderUtil.getProjectId());
        List<BilLiveErrorLogEntity> list =  logDao.getList(po);
        beans.put("vo", list);
        ExcelRender.me("/excel/export/errorlog.xlsx").beans(beans).render(response);
    }

}
