package cn.uone.business.investment.service;

import cn.uone.bean.entity.business.investment.InvestmentResourceEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 品牌库资源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
public interface IInvestmentResourceService extends IService<InvestmentResourceEntity> {

    boolean saveOrUpdate(InvestmentResourceEntity entity, List<MultipartFile> files, List<MultipartFile> image);
}
