package cn.uone.business.bil.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.contract.PlatformEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderItemPreviewEntity;
import cn.uone.bean.entity.business.bil.BilOrderPreviewEntity;
import cn.uone.bean.entity.business.cont.vo.ContRentLadderVo;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.business.bil.dao.BilOrderPreviewDao;
import cn.uone.business.bil.service.*;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysDictDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 租金主账单预览表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Service
public class BilOrderPreviewByContMonthServiceImpl extends ServiceImpl<BilOrderPreviewDao, BilOrderPreviewEntity> implements IBilOrderPreviewByContMonthService {

    @Autowired
    IResSourceService resSourceService;
    @Autowired
    @Lazy
    IBilOrderPreviewService bilOrderPreviewService;
    @Autowired
    @Lazy
    IBilOrderItemPreviewService bilOrderItemPreviewService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    private IBilOrderItemPreviewService itemPreviewService;
    @Autowired
    @Lazy
    private IContRentLadderService contRentLadderService;
    @Autowired
    ISysDictDataService sysDictDataService;
    @Autowired
    @Lazy
    IBilOrderService bilOrderService;
    @Autowired
    IBilOrderItemService bilOrderItemService;


    /**
     * 获取账期月数
     * @param payType
     * @return
     */
    private int getOrderPeriods(String payType) {
        int index = 1;
        if (PayTypeEnum.ONE_ONE.getValue().equals(payType) || PayTypeEnum.TWO_ONE.getValue().equals(payType)||PayTypeEnum.SALARY_WITHHOLD.getValue().equals(payType)) {
            //月付
            index = 1;
        } else if (PayTypeEnum.ONE_TWO.getValue().equals(payType)) {
            //押一付二
            index = 2;
        } else if (PayTypeEnum.ONE_THREE.getValue().equals(payType) || PayTypeEnum.TWO_THREE.getValue().equals(payType)) {
            //季付
            index = 3;
        } else if (PayTypeEnum.ONE_SIX.getValue().equals(payType)) {
            //半年付
            index = 6;
        } else if (PayTypeEnum.ONE_TWELVE.getValue().equals(payType)) {
            //年付
            index = 12;
        }
        return index;
    }
    private int getOrderPeriods(String payType,String platform,String projectId,Date startDate) throws Exception {
        int index = 1;
        int start = DateUtil.month(startDate) + 1;
        String projectParaValue = null;
        if(PlatformEnum.YG.getValue().equals(platform)){
            ResProjectParaEntity projectPara = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.YG_SEASON_PAY_TYPE.getValue(),projectId);
            if(projectPara != null){
                projectParaValue = projectPara.getParamValue();
            }
        }
        if (PayTypeEnum.ONE_ONE.getValue().equals(payType) || PayTypeEnum.TWO_ONE.getValue().equals(payType)||PayTypeEnum.SALARY_WITHHOLD.getValue().equals(payType)) {
            //月付
            index = 1;
        } else if (PayTypeEnum.ONE_TWO.getValue().equals(payType)) {
            //押一付二
            index = 2;
        } else if (PayTypeEnum.ONE_THREE.getValue().equals(payType) || PayTypeEnum.TWO_THREE.getValue().equals(payType)) {
            //季付
            index = 3;
        } else if (PayTypeEnum.ONE_SIX.getValue().equals(payType)) {
            //半年付
            index = 6;
        } else if (PayTypeEnum.ONE_TWELVE.getValue().equals(payType)) {
            //年付
            index = 12;
        }
        if("2".equals(projectParaValue)){
            int remainder = start % index;
            index = remainder == 0?1:index - remainder + 1;
        }
        return index;
    }


    /**
     * 合同月算法
     * @param startDate
     * @param endDate
     * @param freeEndDate
     * @param payType
     * @param price
     * @param projectId
     * @param sourceId
     * @param platform
     * @return
     * @throws Exception
     */
    public List<BilOrderPreviewEntity> createRentOrderPreview(Date startDate,Date endDate,Date freeEndDate,String payType,BigDecimal price,String projectId,String sourceId,String platform) throws Exception {
        List<BilOrderPreviewEntity> orderList = Lists.newArrayList();
        List<ContRentLadderVo> rents = Lists.newArrayList();
        ContRentLadderVo rentLadderVo = new ContRentLadderVo();
        rentLadderVo.setStartDate(startDate);
        rentLadderVo.setEndDate(endDate);
        rentLadderVo.setPrice(price);
        rents.add(rentLadderVo);
        Boolean isFirst = true;
        Boolean isLast = false;
        int month = getOrderPeriods(payType);
        //Boolean needUp = isFirst && DateUtil.beginOfMonth(startDate).compareTo(startDate) != 0;
        //判断是否有免租期，有的话则首期账单添加免租明细
        BilOrderItemPreviewEntity freeItem = null;
        if(freeEndDate != null){
            freeItem = itemPreviewService.createRentOrderItem(BigDecimal.ZERO,startDate,freeEndDate);
            startDate = DateUtil.offsetDay(DateUtil.beginOfDay(freeEndDate), 1);
        }
        int day = DateUtil.dayOfMonth(startDate);
        for (; startDate.compareTo(endDate) <= 0; ) {
            Date orderStartDate = startDate;
            Date orderEndDate = null;
            BigDecimal rentPayment = BigDecimal.ZERO;
            List<BilOrderItemPreviewEntity> items = Lists.newArrayList();
            //判断是否有免租期，有的话则首期账单添加免租明细
            if(isFirst && freeItem != null){
                items.add(freeItem);
                orderStartDate = freeItem.getStartTime();
            }
            //账单明细
            int index =  month;
            for (int i = 0; i < index; i++) {
                int dayi = DateUtil.dayOfMonth(startDate);
                if(dayi != day && dayi == 1){
                    orderEndDate = DateUtil.offsetDay(startDate, day-2);
                }else{
                    orderEndDate = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.offsetMonth(startDate,1),-1));
                }
                BigDecimal monthPayment = price;
                if(DateUtil.compare(orderEndDate,endDate)==1){
                    orderEndDate = endDate;
                    monthPayment = contRentLadderService.getMonthRentByProject(projectId,rents,startDate,orderEndDate);
                }
                BilOrderItemPreviewEntity item = itemPreviewService.createRentOrderItem(monthPayment,startDate,orderEndDate);
                rentPayment = rentPayment.add(monthPayment);
                items.add(item);

                startDate = DateUtil.offsetDay(orderEndDate, 1);
                //如果起始时间大于合同结束时间
                if (startDate.compareTo(endDate) > 0) {
                    isLast = true;
                    break;
                }
            }
            BilOrderPreviewEntity order = bilOrderPreviewService.createOrderPreview(OrderTypeEnum.RENT.getValue(),rentPayment,orderStartDate,null,projectId,sourceId,null,isFirst,items);
            order.setStartDate(orderStartDate);
            order.setEndDate(DateUtil.beginOfDay(orderEndDate));
            order.setInitial(isLast);
            order.setId(IdUtil.simpleUUID());
            orderList.add(order);
            isFirst = false;
        }
        return orderList;
    }
}
