package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.tpi.record.RecordPo;
import cn.uone.bean.parameter.RecordManagePo;
import cn.uone.bean.parameter.RecordManageVo;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * 合同信息
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IContContractInfoService extends IService<ContContractInfoEntity> {


    /**
     * 根据合同id查看合同信息
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    ContContractInfoEntity getByContractId(String contractId);

    /**
     * 备案回调
     *
     * @param htId （用户名+ID）
     */
    void callbackRecord(String htId, String msg, String state, String bah);

    void callbackRecord2(String htId, String msg, String state, String bah,String ywlx,String modelId);

    IPage<RecordManageVo> selectRecord(Page page, RecordManagePo p);

    List<RecordManageVo> selectRecord(RecordManagePo p);

    RecordPo getRecordInfoByCsId(String csId) throws BusinessException;

    ContContractInfoEntity handleContractInfo(ContContractInfoEntity contractInfo, String contractId, SaleDemandEntity demand, RenterEntity renter, ContTempEntity templet, String projectId);
}
