package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.vo.ContRentLadderVo;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 适用于漳州城投
 * 服务类
 * 租金阶梯
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-05-22
 */
public interface IZzctContRentLadderService extends IService<ContRentLadderEntity> {

    @Transactional
    boolean addMultiContRentLadder(ContContractEntity contract, ContContractSourceRelEntity rel, BigDecimal price, List<ContRentLadderEntity> rentLadders) throws Exception;

    /**
     * 保存房源租金表
     * @param contractSourceId
     * @param price
     * @param contract
     */
    void saveContRentLadders(String contractSourceId, BigDecimal price, ContContractEntity contract);

    /***
     * 获取时间段内的租金对象
     * @param contractSourceId 合同房源ID
     * @param sDate 开始时间
     * @param eDate 结束时间
     * @return
     * @throws ParseException
     */
    ContRentLadderVo getRentLadder(String contractSourceId, Date sDate, Date eDate,int addDays);

    ContRentLadderVo getRentLadder(String contractSourceId, Date sDate, Date eDate,BigDecimal subsidy,int addDays);

}
