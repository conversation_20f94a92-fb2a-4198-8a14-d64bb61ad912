package cn.uone.business.cont.controller.zzct;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.*;
import cn.uone.application.enumerate.contract.*;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.*;
import cn.uone.bean.entity.business.cont.dto.ContractDTO;
import cn.uone.bean.entity.business.cont.vo.ContCheckInUserVo;
import cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.cont.vo.ReplaceRenterVo;
import cn.uone.bean.entity.business.demo.DemoContractEntity;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.tpi.fadada.ReqExtsign;
import cn.uone.bean.entity.tpi.fadada.ReqUploadDocs;
import cn.uone.bean.parameter.ContractSignVo;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IZzctBilOrderService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.controller.ContContractController;
import cn.uone.business.cont.service.*;
import cn.uone.business.cont.task.ContractTask;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.business.util.ContractUtil;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.bus.IContContractFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.util.CodeUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.DateTimeUtil;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.PassWordCreateUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 部分方法从漳州城投工程迁移过来
 * 前端控制器sign
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-05-24
 */
@Api(value = "合同服务", tags = {"合同新增、修改等接口"})
@RestController
@RequestMapping("/zzct/cont/contract")
public class ZzctContContractController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ContContractController.class);

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private IContContractService contractService;
    @Autowired
    private IBaseEnterpriseService enterpriseService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IContContractSourceRelService contractSourceRelService;
    @Autowired
    private IResSourceService sourceService;
    @Autowired
    private IContContractInfoService contractInfoService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private IResProjectInfoService projectInfoService;
    @Autowired
    private IContCheckInUserService checkInUserService;
    @Autowired
    private IBilOrderAutoService bilOrderAutoService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private IContRentLadderService contRentLadderService;
    @Autowired
    private IResProjectInfoService iResProjectInfoService;
    @Autowired
    private IResSourceService resSourceService;
    @Resource
    private IRenterFegin iRenterFegin;
    @Resource
    private IFadadaFegin iFadadaFegin;
    @Resource
    private ResSourceDao resSourceDao;
    @Autowired
    private IResProjectInfoService resProjectInfoService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    private IResProjectService resProjectService;
    @Value("${project.uoneTianDi.id}")
    private String uoneTianDiId;
    @Autowired
    private IBizReleaseService releaseService;
    @Autowired
    private ContractTask task;
    @Autowired
    private IDemoContractService demoContractService;
    @Resource
    private IRenterFegin renterFegin;
    @Resource
    private IWechatFegin wechatFegin ;
    @Autowired
    private ISysFileService fileService;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    private IZzctBilOrderService zzctBilOrderService;


    /**
     * 更换承租人
     * @param vo
     * @param files
     * @return
     * @throws Exception
     */
    @PostMapping("/replaceRenter")
    public RestResponse replaceRenter(@ModelAttribute ReplaceRenterVo vo,
                                      @RequestParam(required = false) List<MultipartFile> files) throws Exception {

        //根据手机号 查询租客
        RenterEntity renterEntity = renterFegin.getByTelAndType(vo.getNewTel(),RenterType.COMMON.getValue());
        //查询合同
        ContContractEntity contractEntity =  contractService.getById(vo.getContractId());
        //查询合同信息表
        ContContractInfoEntity infoEntity =  contractInfoService.getByContractId(vo.getContractId());
        //查询未推送账单
        List<BilOrderEntity> bilOrderEntityList = zzctBilOrderService.getByContractIdAndNoPush(vo.getContractId());
        if(contractEntity ==null ){
            RestResponse.failure("合同不存在");
        }
        // 判断租客用户是否存在,不存在则新增租客用户
        renterEntity = contractService.judgeRenter(vo.getNewTel(),renterEntity,vo.getNewName(),"1",vo.getNewIdCard());
        //更新合同
        contractEntity.setSignerId(renterEntity.getId());//更新合同表签约用户id
        contractEntity.setOldRenterId(vo.getOldRenterId());//保存原租客id
        contractEntity.updateById();
        //更新合同信息表
        infoEntity.setName(vo.getNewName());
        infoEntity.setIdNo(vo.getNewIdCard());
        infoEntity.setTel(vo.getNewTel());
        infoEntity.updateById();
        //更新未推送订单表
        for (BilOrderEntity o:bilOrderEntityList) {
            o.setPayerId(renterEntity.getId());
            o.setOldRenterId(vo.getOldRenterId());
            o.updateById();
        }
        if(files != null &&  files.size() > 0){
            fileService.saveFiles(files, vo.getContractId(), SysFileTypeEnum.SUBJOIN_CONTRACT.getValue());
        }
        return RestResponse.success();
    }

    // 查询合同
    @GetMapping("/list")
    public RestResponse list(HttpServletRequest request) throws Exception {
        Page<ContContractEntity> page = new Page<>();
        page.setCurrent(Long.parseLong(request.getParameter("current")));
        page.setSize(Long.parseLong(request.getParameter("size")));

        Map<String, Object> map = getParaToMap(request);
        map.put("isZzct","zzct");
        IPage<ContContractVo> iPage = contractService.selectContContract(page, map);
        return RestResponse.success("查询成功").setData(iPage);
    }

    //获取原租客的详细信息
    @RequestMapping(value = "/getRenterInfo")
    public RestResponse getRenterInfo(String id) throws Exception {
        RenterEntity renterEntity = renterFegin.getById(id);
        return RestResponse.success().setData(renterEntity);
    }

    // 附件删除
    @PostMapping("/deleteFile")
    @Transactional
    public RestResponse deleteFile(@RequestParam String fileId) {
        try{
            if(StrUtil.isNotBlank(fileId)){
                SysFileEntity entity = sysFileService.getById(fileId);
                //FileUtil.delete(entity.getUrl());
                if(entity != null){
                    minioUtil.delete(entity.getUrl());
                    sysFileService.removeById(fileId);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
        return RestResponse.success("附件删除成功！");
    }

    @UoneLog("合同导出")
    @PostMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> map = getParaToMap(request);
        List<ContContractVo> list = contractService.exportContContract(map);
        for(ContContractVo item:list){
            if(BaseConstants.BOOLEAN_OF_FALSE.equals(item.getIsOrganize())){
                item.setIsOrganizeStr("个人");
            }else{
                item.setIsOrganizeStr("机构");
            }
            if(BooleanUtils.isTrue(item.getIsSubsidy())){
                item.setIsSubsidyStr("是");
            }else{
                item.setIsSubsidyStr("否");
            }
            item.setContractTypeStr(ContractTypeEnum.getNameByValue(item.getContractType()));
            item.setStateStr(ContractStateEnum.getNameByValue(item.getState()));
            item.setPayTypeStr(PayTypeEnum.getNameByValue(item.getPayType()));//缴费方式

            item.setCreateType(ContractCreateTypeEnum.getNameByValue(item.getCreateType()));

            if(null!=item.getCheckoutDate()&&!item.getReleaseState().equals(ReleaseStateEnum.CANCEL.getValue())){
                item.setCheckInDay(DateUtil.betweenDay(item.getStartDate(),item.getCheckoutDate(),true)+1+"");
            }else{
                item.setCheckInDay(DateUtil.betweenDay(item.getStartDate(),item.getEndDate(),true)+1+"");
            }
        }
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        if(map.get("isShort").equals("1")){
            ExcelRender.me("/excel/export/contract1.xlsx").beans(beans).render(response);
        }else{
            ExcelRender.me("/excel/export/contract_zzct.xlsx").beans(beans).render(response);
        }

    }

    private Map<String,Object> getParaToMap(HttpServletRequest request){
        Map<String, Object> map = Maps.newHashMap();
        if(StrUtil.isNotBlank(request.getParameter("ids"))){
            String[] split = request.getParameter("ids").split(",");
            List<String> idList = new ArrayList<>(Arrays.asList(split));
            map.put("ids", idList);
        }
        if(StrUtil.isNotBlank(request.getParameter("contractSourceIds"))){
            String[] split = request.getParameter("contractSourceIds").split(",");
            List<String> idList = new ArrayList<>(Arrays.asList(split));
            map.put("contractSourceIds", idList);
        }
        String projectId = UoneHeaderUtil.getProjectId();
        if(StrUtil.isBlank(projectId)){
            projectId = StrUtil.isNotBlank(request.getParameter("projectId"))? request.getParameter("projectId"):null;
        }
        map.put("frameContractId", StrUtil.isNotBlank(request.getParameter("frameId")) ? request.getParameter("frameId") : null);
        map.put("isShort", StrUtil.isNotBlank(request.getParameter("isShort")) ? request.getParameter("isShort") : null);
        map.put("platform",StrUtil.isNotBlank(request.getParameter("platform"))? request.getParameter("platform"):null);
        map.put("projectId", projectId);
        map.put("partitionId", StrUtil.isNotBlank(request.getParameter("partitionId"))? request.getParameter("partitionId"):null);
        map.put("antistop", StrUtil.isNotBlank(request.getParameter("antistop"))?request.getParameter("antistop"):null);//关键字
        map.put("platformCode", StrUtil.isNotBlank(request.getParameter("platformCode"))?request.getParameter("platformCode"):null);//关键字
        map.put("key", StrUtil.isNotBlank(request.getParameter("key"))?request.getParameter("key"):null);//关键字
        map.put("isOrganize", StrUtil.isNotBlank(request.getParameter("isOrganize"))?request.getParameter("isOrganize"):null);
        map.put("contractType", StrUtil.isNotBlank(request.getParameter("contractType"))?request.getParameter("contractType"):null);
        map.put("state", StrUtil.isNotBlank(request.getParameter("state"))?request.getParameter("state"):null);
        map.put("isSubsidy", StrUtil.isNotBlank(request.getParameter("isSubsidy"))?request.getParameter("isSubsidy"):null);//是否是人才 用是否有补贴判断
        //审核时间
        map.put("signDateStart", !StrUtil.isBlank(request.getParameter("signDateStart")) ? DateUtil.parse(request.getParameter("signDateStart"), "yyyy-MM-dd") : null);
        map.put("signDateEnd", !StrUtil.isBlank(request.getParameter("signDateEnd")) ? DateUtil.parse(request.getParameter("signDateEnd"), "yyyy-MM-dd") : null);
        //租赁起始日
        map.put("startDate", !StrUtil.isBlank(request.getParameter("startDate")) ? DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd") : null);
        map.put("endDate", !StrUtil.isBlank(request.getParameter("endDate")) ? DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd") : null);
        //起始日
        map.put("startDateStart", !StrUtil.isBlank(request.getParameter("startDateStart")) ? DateUtil.parse(request.getParameter("startDateStart"), "yyyy-MM-dd") : null);
        map.put("startDateEnd", !StrUtil.isBlank(request.getParameter("startDateEnd")) ? DateUtil.parse(request.getParameter("startDateEnd"), "yyyy-MM-dd") : null);
        //到期日
        map.put("endDateStart", !StrUtil.isBlank(request.getParameter("endDateStart")) ? DateUtil.parse(request.getParameter("endDateStart"), "yyyy-MM-dd") : null);
        map.put("endDateEnd", !StrUtil.isBlank(request.getParameter("endDateEnd")) ? DateUtil.parse(request.getParameter("endDateEnd"), "yyyy-MM-dd") : null);
        //退房日期
        map.put("checkoutDateStart", !StrUtil.isBlank(request.getParameter("checkoutDateStart")) ? DateUtil.parse(request.getParameter("checkoutDateStart"), "yyyy-MM-dd") : null);
        map.put("checkoutDateEnd", !StrUtil.isBlank(request.getParameter("checkoutDateEnd")) ? DateUtil.parse(request.getParameter("checkoutDateEnd"), "yyyy-MM-dd") : null);
        //终止时间
        map.put("stopTimeStart", !StrUtil.isBlank(request.getParameter("stopTimeStart")) ? DateUtil.parse(request.getParameter("stopTimeStart"), "yyyy-MM-dd") : null);
        map.put("stopTimeEnd", !StrUtil.isBlank(request.getParameter("stopTimeEnd")) ? DateUtil.parse(request.getParameter("stopTimeEnd"), "yyyy-MM-dd") : null);
        map.put("orderName", StrUtil.isNotBlank(request.getParameter("orderName"))?request.getParameter("orderName"):null);
        map.put("orderType", StrUtil.isNotBlank(request.getParameter("orderType"))?request.getParameter("orderType"):null);


        map.put("keyCode", StrUtil.isNotBlank(request.getParameter("keyCode")) ? request.getParameter("keyCode") : null);

        map.put("sourceId", StrUtil.isNotBlank(request.getParameter("sourceId")) ? request.getParameter("sourceId") : null);

        return map;
    }


}
