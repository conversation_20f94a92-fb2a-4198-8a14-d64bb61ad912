package cn.uone.business.task.service.impl;

import cn.uone.bean.entity.business.task.TaskDetailsEntity;
import cn.uone.business.task.dao.TaskDetailsDao;
import cn.uone.business.task.service.ITaskDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Service
public class TaskDetailsServiceImpl extends ServiceImpl<TaskDetailsDao, TaskDetailsEntity> implements ITaskDetailsService {

    @Autowired
    TaskDetailsDao taskDetailsDao;

    @Override
    public TaskDetailsEntity getByPlanId(String planId) {
        return taskDetailsDao.getByPlanId(planId);
    }
}
