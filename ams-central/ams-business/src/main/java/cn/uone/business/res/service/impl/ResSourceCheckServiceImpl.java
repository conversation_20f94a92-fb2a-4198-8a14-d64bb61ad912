package cn.uone.business.res.service.impl;

import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityImportVo;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntitySerchVo;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityVo;
import cn.uone.bean.entity.business.res.ResSourceCheckEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceCheckSearchVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.business.res.dao.ResSourceCheckDao;
import cn.uone.business.res.service.IResSourceCheckService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class ResSourceCheckServiceImpl extends ServiceImpl<ResSourceCheckDao, ResSourceCheckEntity> implements IResSourceCheckService {



    @Override
    public IPage<ResSourceVo> listPage(Page page, ResSourceCheckSearchVo vo) {
        DataScope dataScope=new DataScope(UoneSysUser.id());
        dataScope.setProAlias("s");
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.list(page,dataScope,vo);
    }

    @Override
    public void upstate(Map<String, Object> map, List list) {
        baseMapper.upstate(map,list);
    }

    @Override
    public IPage<DevDeviceEntityVo> relationDevice(Page page, DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        IPage<DevDeviceEntityVo> IPage = baseMapper.relationDevicePage(page,serchVo);
//        for(int i=0;i<IPage.getRecords().size();i++){
//            File f = new File("C:\\Users\\<USER>\\AppData\\Roaming\\codeImg");
//            // 创建文件夹
//            if (!f.exists() && !f.isDirectory()) {
//                f.mkdirs();
//            }
//            String http = "http://zfgl.domeke.com/com/#/pages2/personal/repair/code?id="; //请根据真实接口进行改写
//            String id = IPage.getRecords().get(i).getId(); //二维码id
//            String urlId = http+id; //跳转路径
//            String path =f.getPath()+"/"+id +".png";
//            if(!new File(path).exists()){
//                try {
//                    generateQRCodeImage(urlId,400,500,path);
//                } catch (WriterException e) {
//                    e.printStackTrace();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            IPage.getRecords().get(i).setCodeUrl(path);
//        }
        return IPage;
    }

    @Override
    public IPage<DevDeviceEntityVo> relationDeviceList(Page page, DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        IPage<DevDeviceEntityVo> IPage = baseMapper.relationDeviceList(page,serchVo);
//        for(int i=0;i<IPage.getRecords().size();i++){
//            File f = new File("C:\\Users\\<USER>\\AppData\\Roaming\\codeImg");
//            // 创建文件夹
//            if (!f.exists() && !f.isDirectory()) {
//                f.mkdirs();
//            }
//            String http = "http://localhost:8080/#/pages2/personal/repair/code?id="; //请根据真实接口进行改写
//            String id = IPage.getRecords().get(i).getId(); //二维码id
//            String urlId = http+id; //跳转路径
//            String path =f.getPath()+"/"+id +".png";
//            if(!new File(path).exists()){
//                try {
//                    generateQRCodeImage(urlId,400,500,path);
//                } catch (WriterException e) {
//                    e.printStackTrace();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            IPage.getRecords().get(i).setCodeUrl(path);
//        }
        return IPage;
    }

    @Override
    public IPage<DevDeviceEntityVo> relationSourcePage(Page page, DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.relationSourcePage(page,serchVo);
    }

    @Override
    public IPage<DevDeviceEntityVo> relationPublicPage(Page page, DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.relationPublicPage(page,serchVo);
    }

    @Override
    public List<DevDeviceEntityVo> importDevice(DevDeviceEntityImportVo vo) {
        return baseMapper.importDevice(vo);
    }

    @Override
    public DevDeviceEntityVo importZnDevice(DevDeviceEntityImportVo vo) {
        return baseMapper.importZnDevice(vo);
    }


    /**
     * 生成一个二维码
     * @param text 你需要转换的字符串文本
     * @param width 二维码宽度
     * @param height 二维码高度
     * @param filePath 需要二维码存放的路径
     * @throws WriterException
     * @throws IOException
     */
    public void generateQRCodeImage(String text, int width, int height, String filePath) throws WriterException, IOException {
        //定义二维码的参数
        HashMap hints = new HashMap();
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.MARGIN, 2);

        QRCodeWriter qrCodeWriter = new QRCodeWriter();

        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height,hints);

        Path path = FileSystems.getDefault().getPath(filePath);

        MatrixToImageWriter.writeToPath(bitMatrix, "PNG", path);

    }

    /**
     * 获取房源关联的设备列表
     * 不分页
     * @param serchVo
     * @return
     */
    @Override
    public List<DevDeviceEntityVo> getRelationDeviceList(DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        List<DevDeviceEntityVo> deviceList = baseMapper.relationDevicePage(serchVo);
        return deviceList;
    }
}
