package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.util.wechat.ApprovalStateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 确认退款
 */
@Service
public class OrderRefundtion implements Operation {

    @Resource
    private IBilOrderService bilOrderService ;
    @Resource
    private IApprovalCommitService approvalCommitService ;
    @Resource
    private IContContractService contractService;
    @Resource
    private IApprovalProjectParaService projectParaService;

    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        BilOrderEntity entity=bilOrderService.getById(expression.getCodeId());
        ApprovalCommitEntity commitEntity=null;
        if(ObjectUtil.isNotNull(entity)){
            commitEntity=approvalCommitService.getCommitEntity(expression.getCodeId(),ApprovalTypeEnum.ORDERCONFIRMREFUND.getValue());
            ApprovalProjectParaEntity temp=projectParaService.get(entity.getSourceId(),ApprovalTemplateEnum.ORDERCONFIRMREFUND.getType(),false);
            if(commitEntity==null){
                commitEntity=new ApprovalCommitEntity();
                ContContractEntity contract = contractService.getById(entity.getContractId());
                ApprovalStateUtil.initCommit(commitEntity,expression.getCodeId());
                commitEntity.setApprovalNum(entity.getCode());
                commitEntity.setPrice(entity.getPayment());
                commitEntity.setSigner(contract.getSignerId());
                commitEntity.setPayTime(expression.getApplyTime());
                commitEntity.setTemplateid(temp.getApprovalTempId());
                commitEntity.setType(ApprovalTypeEnum.ORDERCONFIRMREFUND.getValue());
                commitEntity.setTableName(ApprovalTypeEnum.ORDERCONFIRMREFUND.getTable());
                commitEntity.setTitle(ApprovalTypeEnum.ORDERCONFIRMREFUND.getName());
                commitEntity.setTitle1("账单编号:"+entity.getCode());
                commitEntity.setTitle2("账单类型:"+ OrderTypeEnum.getNameByValue(entity.getOrderType()));
                commitEntity.setTitle3("确认退款原因:"+expression.getMemo());
                approvalCommitService.save(commitEntity);
            }else{
                //重新提交审批（单号不能重复）
                commitEntity.setPrice(entity.getPayment());
                commitEntity.setPayTime(expression.getApplyTime());
                approvalCommitService.resetInitCommit(commitEntity,"账单编号:"+entity.getCode(),"账单类型:"+ OrderTypeEnum.getNameByValue(entity.getOrderType()),"确认退款原因:"+expression.getMemo(),null);
                approvalCommitService.updateById(commitEntity);
            }
        }
        return commitEntity;
    }


}
