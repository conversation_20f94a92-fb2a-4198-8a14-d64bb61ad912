package cn.uone.business.cont.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.MsgTypeEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.CheckInUserTypeEnum;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.vo.ContCheckInUserVo;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.util.AlgorUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.PassWordCreateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 入住用户
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/cont-check-in-user-entity")
public class ContCheckInUserController extends BaseController {

    @Autowired
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    IContContractSourceRelService contractSourceRelService;
    @Autowired
    IResSourceService sourceService;

    /**
     * 入住人维护详情
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 18:13
     * @Param:
     */
    @PostMapping("/judge")
    public RestResponse judge(@RequestParam("id") String contractSourceId) {
        // 参数判断
        if (StrUtil.isBlank(contractSourceId)) {
            return RestResponse.failure("参数异常");
        }
        List<ContCheckInUserEntity> userList = contCheckInUserService.getByContractSourceId(contractSourceId);
        ContContractSourceRelEntity contractSourceRel = contractSourceRelService.getById(contractSourceId);
        ResSourceEntity sourceEntity =sourceService.getSourceByContractId(contractSourceRel.getContractId());
        if(userList!=null&&userList.size()>=sourceEntity.getCheckinMax()){
            return RestResponse.failure("已到达入住人上限，无法添加！");
        }
        return RestResponse.success();
    }


    @PostMapping("/add")
    public RestResponse add(@ModelAttribute ContCheckInUserEntity entity,@RequestParam(required = false) List<MultipartFile> imageFile){
        if(imageFile.size()!=2){
            return RestResponse.failure("请上传身份证正反面各一张（限2张）");
        }
        //判断是否重复
        List<ContCheckInUserEntity> users=contCheckInUserService.getByContractSourceId(entity.getContractSourceId());
        for (ContCheckInUserEntity old:users) {
                if(entity.getIdNo().equals(old.getIdNo())){
                    return RestResponse.failure("填写错误！身份证号码与其他入住人重复,请修改");
                }
                if(entity.getTel().equals(old.getTel())){
                    return RestResponse.failure("填写错误！手机号与其他入住人重复,请修改");
                }
        }

            QueryWrapper<ContCheckInUserEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("contract_source_id", entity.getContractSourceId());
            queryWrapper.eq("tel", entity.getTel());
            if (StringUtils.isNotBlank(entity.getId())) {
                queryWrapper.ne("id", entity.getId());
            }else{
                //id为空,说明是新增同住人
                //删除该手机号的,,该房源,已搬离的同住人信息
                QueryWrapper<ContCheckInUserEntity> delRemove = new QueryWrapper();
                delRemove.eq("contract_source_id", entity.getContractSourceId());
                delRemove.eq("tel", entity.getTel());
                delRemove.eq("type",CheckInUserTypeEnum.REMOVE.getValue());
                delRemove.eq("state",ApprovalStateEnum.COMPLETE.getValue());
                contCheckInUserService.remove(delRemove);

                //删除该手机号的,,该房源,被拒绝的同住人信息
                QueryWrapper<ContCheckInUserEntity> delCheckIn = new QueryWrapper();
                delCheckIn.eq("contract_source_id", entity.getContractSourceId());
                delCheckIn.eq("tel", entity.getTel());
                delCheckIn.eq("type",CheckInUserTypeEnum.CHECKIN.getValue());
                delCheckIn.eq("state",ApprovalStateEnum.REJECT.getValue());
                contCheckInUserService.remove(delCheckIn);
            }
            //提交时间更新
            entity.setCreateDate(new Date());
            long countTotal = contCheckInUserService.count(queryWrapper);
            //该手机号 已搬离 或者 入住审核不通过 的条数
            queryWrapper.and(query ->
                    query.and(checkIn ->
                            checkIn.eq("type", CheckInUserTypeEnum.CHECKIN.getValue()).eq("state", ApprovalStateEnum.REJECT.getValue())
                    ).or().nested(remove->
                            remove.eq("type", CheckInUserTypeEnum.REMOVE.getValue()).eq("state", ApprovalStateEnum.COMPLETE.getValue())
                    )
            );
            long count1 = contCheckInUserService.count(queryWrapper);
            long count = countTotal - count1;
            if (count > 0) {
                return RestResponse.failure("同住人已存在该手机号信息");
            }
            entity.setCheckInDate(new Date());
            entity.setState("2");//ApprovalStateEnum.COMPLETE.getValue()
            entity.setType("10");
            entity.setCheckin(true);
            contCheckInUserService.save(entity);

//            ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(request.getParameter("contractSourceId"));
            // 消息推送
//            sysPushMsgService.createPushMsg(MsgTypeEnum.XINZENGRUZHURENSHENQING.getValue(), null, contContractSourceRel.getSourceId());
        contCheckInUserService.addImg(entity.getId(),imageFile);
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse addOrUpdate(@ModelAttribute ContCheckInUserEntity entity,@RequestParam(required = false) List<MultipartFile> imageFile){
        if(imageFile.size()!=0&&imageFile.size()!=2){
            return RestResponse.failure("请上传身份证正反面各一张（限2张）");
        }
        List<ContCheckInUserEntity> users=contCheckInUserService.getByContractSourceId(entity.getContractSourceId());
        for (ContCheckInUserEntity old:users) {
            if(!old.getId().equals(entity.getId())){
                if(entity.getIdNo().equals(old.getIdNo())){
                    return RestResponse.failure("填写错误！身份证号码与其他入住人重复,请修改");
                }
                if(entity.getTel().equals(old.getTel())){
                    return RestResponse.failure("填写错误！手机号与其他入住人重复,请修改");
                }
            }
            //租客表不存在需要添加到租客表
            RenterEntity checkInRenter = null;
            try {
                checkInRenter = renterFegin.getByTelAndType(entity.getTel(), RenterType.COMMON.getValue());
                if (checkInRenter == null) {
                    // 新增租客用户
                    checkInRenter = new RenterEntity();
                    checkInRenter.setName(entity.getName())
                            .setTel(entity.getTel())
                            .setIdType(entity.getIdType())
                            .setIdNo(entity.getIdNo())
                            .setType(RenterType.COMMON.getValue())
                            .setUsername(entity.getTel())
                            .setSalt(AlgorUtil.getSalt())
                            .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成
                    RestResponse res = renterFegin.add(checkInRenter);
                    checkInRenter = JSONUtil.toBean(JSONUtil.parseObj(res.get("data")),RenterEntity.class);
                } else {
                    //修改租客信息为用户最新信息
                    checkInRenter.setName(entity.getName())
                            .setIdType(entity.getIdType())
                            .setIdNo(entity.getIdNo());
                    checkInRenter = renterFegin.update(checkInRenter);
                }
                entity.setRenterId(checkInRenter.getId());
            } catch (Exception e) {
                e.printStackTrace();
                return RestResponse.failure("修改租客信息异常");
            }
        }
        contCheckInUserService.edit(entity,imageFile);
        return RestResponse.success();
    }
    /**
     * 小程序签约新增同住人
     */
    @RequestMapping("/addBySign")
    @UoneLog("签约新增同住人")
    public RestResponse addBySign(@RequestBody ContCheckInUserVo checkInUserVo) throws Exception {
        ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.getListByContractId(checkInUserVo.getContractid()).get(0);
        //添加同住人
            if (checkInUserVo.getTel().equals(checkInUserVo.getSignTel())) {
                RestResponse.failure("同住人手机号不能与签约人一致");
            }
            ContCheckInUserEntity check = new ContCheckInUserEntity();
            RenterEntity checkInRenter = renterFegin.getByTelAndType(checkInUserVo.getTel(), RenterType.COMMON.getValue());
            if (checkInRenter == null) {
                // 新增租客用户
                checkInRenter = new RenterEntity();
                checkInRenter.setName(checkInUserVo.getName())
                        .setTel(checkInUserVo.getTel())
                        .setIdType(checkInUserVo.getIdType())
                        .setIdNo(checkInUserVo.getIdNo())
                        .setType(RenterType.COMMON.getValue())
                        .setUsername(checkInUserVo.getTel())
                        .setSalt(AlgorUtil.getSalt())
                        .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成
                checkInRenter = JSON.parseObject(JSON.toJSONString(renterFegin.add(checkInRenter).get("data")), RenterEntity.class);
            } else {
                //修改租客信息为用户最新信息
                checkInRenter.setName(checkInUserVo.getName())
                        .setIdType(checkInUserVo.getIdType())
                        .setIdNo(checkInUserVo.getIdNo());
                renterFegin.update(checkInRenter);
            }
            check.setContractSourceId(contractSourceRelEntity.getId())
                    .setState(ApprovalStateEnum.APPROVAL.getValue())
                    .setType(CheckInUserTypeEnum.CHECKIN.getValue())
                    .setTel(checkInUserVo.getTel())
                    .setIdNo(checkInUserVo.getIdNo())
                    .setName(checkInUserVo.getName())
                    .setSex(checkInUserVo.getSex())
                    .setIdType(checkInUserVo.getIdType())
                    .setRenterId(checkInRenter.getId())
                    .setCheckin(true)
                    .setState("2")
                    .setType("10");
            if(StrUtil.isNotBlank(checkInUserVo.getId())){
                   check.setId(checkInUserVo.getId());
            }
        contCheckInUserService.saveOrUpdate(check);
        //删除附件id
        if(checkInUserVo.getDelFileIds()!=null&&checkInUserVo.getDelFileIds().size()>0){
            sysFileService.delFileByFileIds(checkInUserVo.getDelFileIds());
        }
        return RestResponse.success("保存成功").setData(check.getId());
    }
    /**
     * 小程序签约删除同住人
     */
    @RequestMapping("/delBySign")
    @UoneLog("签约删除同住人")
    public RestResponse delBySign(ContCheckInUserEntity checkInUser){
        //删除图片
        sysFileService.delFileByFromIdAndType(checkInUser.getId(),SysFileTypeEnum.CHECKIN_ID_CARD);
        //删除数据
        contCheckInUserService.removeById(checkInUser.getId());
        return RestResponse.success("删除成功");
    }
    /**
     * 删除同住人照片
     */
    @RequestMapping("/delPicById")
    @UoneLog("删除同住人照片")
    public RestResponse delPicByIds(List<String> ids){
        sysFileService.delFileByFileIds(ids);
        return RestResponse.success();
    }
    /**
     * 删除照片
     */
    @RequestMapping("/delPortrait")
    @UoneLog("删除免冠照片")
    public RestResponse delPortrait(@RequestBody ContCheckInUserVo checkInUserVo) throws Exception {
        //删除附件id
        if(checkInUserVo.getDelFileIds()!=null&&checkInUserVo.getDelFileIds().size()>0){
            sysFileService.delFileByFileIds(checkInUserVo.getDelFileIds());
        }
        return RestResponse.success();
    }

    /**
     * 保存/重新提交
     */
    @RequestMapping("/save")
    @UonePermissions(value = LoginType.CUSTOM)
    @UoneLog("添加/修改同住人")
    public RestResponse save(ContCheckInUserEntity checkInUser) {
        RestResponse response = new RestResponse();
        try {
            if(StrUtil.isNotBlank(checkInUser.getName())){
                checkInUser.setName(checkInUser.getName().trim());
            }
            QueryWrapper<ContCheckInUserEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("contract_source_id", checkInUser.getContractSourceId());
            queryWrapper.eq("tel", checkInUser.getTel());
            if (StringUtils.isNotBlank(checkInUser.getId())) {
                queryWrapper.ne("id", checkInUser.getId());
            }else{
                //id为空,说明是新增同住人
                //删除该手机号的,,该房源,已搬离的同住人信息
                QueryWrapper<ContCheckInUserEntity> delRemove = new QueryWrapper();
                delRemove.eq("contract_source_id", checkInUser.getContractSourceId());
                delRemove.eq("tel", checkInUser.getTel());
                delRemove.eq("type",CheckInUserTypeEnum.REMOVE.getValue());
                delRemove.eq("state",ApprovalStateEnum.COMPLETE.getValue());
                contCheckInUserService.remove(delRemove);

                //删除该手机号的,,该房源,被拒绝的同住人信息
                QueryWrapper<ContCheckInUserEntity> delCheckIn = new QueryWrapper();
                delCheckIn.eq("contract_source_id", checkInUser.getContractSourceId());
                delCheckIn.eq("tel", checkInUser.getTel());
                delCheckIn.eq("type",CheckInUserTypeEnum.CHECKIN.getValue());
                delCheckIn.eq("state",ApprovalStateEnum.REJECT.getValue());
                contCheckInUserService.remove(delCheckIn);
            }
            //提交时间更新
            checkInUser.setCreateDate(new Date());
            long countTotal = contCheckInUserService.count(queryWrapper);
            //该手机号 已搬离 或者 入住审核不通过 的条数
            queryWrapper.and(query ->
                    query.and(checkIn ->
                            checkIn.eq("type", CheckInUserTypeEnum.CHECKIN.getValue()).eq("state", ApprovalStateEnum.REJECT.getValue())
                    ).or().nested(remove->
                            remove.eq("type", CheckInUserTypeEnum.REMOVE.getValue()).eq("state", ApprovalStateEnum.COMPLETE.getValue())
                    )
            );
            long count1 = contCheckInUserService.count(queryWrapper);
            long count = countTotal - count1;
            if (count > 0) {
                return RestResponse.failure("同住人已存在该手机号信息");
            }
            checkInUser.setCheckin(true);

            //租客表不存在需要添加到租客表
            RenterEntity checkInRenter = renterFegin.getByTelAndType(checkInUser.getTel(), RenterType.COMMON.getValue());
            if (checkInRenter == null) {
                // 新增租客用户
                checkInRenter = new RenterEntity();
                checkInRenter.setName(checkInUser.getName())
                        .setTel(checkInUser.getTel())
                        .setIdType(checkInUser.getIdType())
                        .setIdNo(checkInUser.getIdNo())
                        .setType(RenterType.COMMON.getValue())
                        .setUsername(checkInUser.getTel())
                        .setSalt(AlgorUtil.getSalt())
                        .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成
                RestResponse res = renterFegin.add(checkInRenter);
                checkInRenter = (RenterEntity) res.get("data");
            } else {
                //修改租客信息为用户最新信息
                checkInRenter.setName(checkInUser.getName())
                        .setIdType(checkInUser.getIdType())
                        .setIdNo(checkInUser.getIdNo());
                checkInRenter = renterFegin.update(checkInRenter);
            }
            checkInUser.setRenterId(checkInRenter.getId());
            contCheckInUserService.saveOrUpdate(checkInUser);

//            ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(request.getParameter("contractSourceId"));
            // 消息推送
//            sysPushMsgService.createPushMsg(MsgTypeEnum.XINZENGRUZHURENSHENQING.getValue(), null, contContractSourceRel.getSourceId());

            response.setSuccess(true).setMessage("保存成功！").setData(checkInUser);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 入住人维护列表
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 16:30
     * @Param:
     */
    @GetMapping("/checkInUserList")
    public RestResponse checkInUserList(HttpServletRequest request) throws Exception {
        Map<String, Object> map = Maps.newHashMap();
        //入住人用户状态
        map.put("state", request.getParameter("state"));
        List<ContCheckInUserVo> contCheckInUserList = contCheckInUserService.getCheckInUserList(map);
        return RestResponse.success().setData(contCheckInUserList);
    }

    @GetMapping("/checkInUserPage")
    public RestResponse checkInUserPage(Page page, @RequestParam("state") String state, @RequestParam("type") String type,@RequestParam("dataFroms") String dataFroms,@RequestParam("isOrganize")String isOrganize) {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("state", state);
        map.put("type", type);
        List<String> contractStates = new ArrayList<>();
        contractStates.add(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
        contractStates.add(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
        map.put("contractStates",contractStates);
        map.put("isOrganize",isOrganize);
        if(StringUtils.isNotBlank(dataFroms)){
            String[] dataFromArray = dataFroms.split(",");
            List<String> list = new ArrayList<>(Arrays.asList(dataFromArray));
            map.put("dataFroms",list);
        }
        IPage<ContCheckInUserVo> result = contCheckInUserService.selectPageByMap(page, map);
        for (ContCheckInUserVo vo : result.getRecords()) {
            //身份证
            List<SysFileEntity> idCardFiles = sysFileService.getListByFromIdAndType(vo.getId(), SysFileTypeEnum.CHECKIN_ID_CARD);
            vo.setIdCardImgs(idCardFiles);
            //暂住证
            List<SysFileEntity> temporaryResidenceFiles = sysFileService.getListByFromIdAndType(vo.getId(), SysFileTypeEnum.CHECKIN_TEMPORARY_RESIDENCE);
            vo.setTemporaryResidenceImgs(temporaryResidenceFiles);
        }
        return RestResponse.success().setData(result);
    }

    /**
     * 入住人维护详情
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 18:13
     * @Param:
     */
    @PostMapping("/checkInUserDetail")
    public RestResponse checkInUserDetail(@RequestParam("id") String id) {
        // 参数判断
        if (StrUtil.isBlank(id)) {
            return RestResponse.failure("参数异常");
        }
        ContCheckInUserEntity contCheckInUserEntity = contCheckInUserService.getById(id);
        //身份证
        ContContractSourceRelEntity relEntity = contContractSourceRelService.getById(contCheckInUserEntity.getContractSourceId());
        List<SysFileEntity> idCardFiles = sysFileService.getListByFromIdAndType(contCheckInUserEntity.getId(), SysFileTypeEnum.CHECKIN_ID_CARD);
        if (idCardFiles.size() <= 0) {
            idCardFiles = sysFileService.getListByFromIdAndType(relEntity.getContractId(), SysFileTypeEnum.ID_CARD);
        }
        contCheckInUserEntity.setFileList2(idCardFiles);
        return RestResponse.success().setData(contCheckInUserEntity);
    }

    /**
     * 待新增审核通过
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 16:00
     * @Param:
     */
    @UoneLog("入住人维护--待新增审核通过")
    @PostMapping("/auditingAdd")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse auditingAdd(@RequestParam("id") String id) throws Exception {
        // 参数判断
        if (StrUtil.isBlank(id)) {
            return RestResponse.failure("参数异常");
        }
        ContCheckInUserEntity contCheckInUserEntity = contCheckInUserService.getById(id);
        RenterEntity renterEntity = renterFegin.getByTelAndType(contCheckInUserEntity.getTel(), RenterType.COMMON.getValue());
        if (null == renterEntity) {
            // 新增租客用户
            String password = PassWordCreateUtil.createPassWord(6);
            renterEntity = new RenterEntity();
            renterEntity.setName(contCheckInUserEntity.getName())
                    .setTel(contCheckInUserEntity.getTel())
                    .setIdType(contCheckInUserEntity.getIdType())
                    .setIdNo(contCheckInUserEntity.getIdNo())
                    .setType(RenterType.COMMON.getValue())
                    .setUsername(contCheckInUserEntity.getTel())
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(password); // 随机密码生成
            renterEntity = JSON.parseObject(JSON.toJSONString(renterFegin.add(renterEntity).get("data")), RenterEntity.class);
        }else{
            //未实名认证，则更新用户表信息
            if(BaseConstants.BOOLEAN_OF_FALSE.equals(renterEntity.getIsVerify())){
                renterEntity.setName(contCheckInUserEntity.getName())
                        .setTel(contCheckInUserEntity.getTel())
                        .setIdType(contCheckInUserEntity.getIdType())
                        .setIdNo(contCheckInUserEntity.getIdNo())
                        .setType(RenterType.COMMON.getValue())
                        .setUsername(contCheckInUserEntity.getTel());
                renterFegin.update(renterEntity);
            }
        }
        contCheckInUserEntity.setRenterId(renterEntity.getId());
        contCheckInUserService.updateById(contCheckInUserEntity);
        contCheckInUserService.complete(contCheckInUserEntity);
        return RestResponse.success("审核通过！");
    }

    /**
     * 待搬离审核通过
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 16:00
     * @Param:
     */
    @UoneLog("入住人维护--待搬离审核通过")
    @PostMapping("/auditMoveAway")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse auditMoveAway(@RequestParam("id") String id) throws Exception {
        // 参数判断
        if (StrUtil.isBlank(id)) {
            return RestResponse.failure("参数异常");
        }
        //获取入住人
        ContCheckInUserEntity checkInUser = contCheckInUserService.getById(id);
        //入住完成
        contCheckInUserService.complete(checkInUser);
        return RestResponse.success("审核通过！");
    }

    /**
     * 审核不通过
     *
     * @return
     * <AUTHOR>
     * @date 2018-12-29 16:00
     * @Param:
     */
    @UoneLog("入住人-审核不通过")
    @PostMapping("/auditFailed")
    public RestResponse auditFailed(@RequestParam("id") String id, @RequestParam("type") String type) {
        // 参数判断
        if (StrUtil.isBlank(id)) {
            return RestResponse.failure("参数异常");
        }
        if (!"20".equals(type) && !"10".equals(type)) {
            return RestResponse.failure("类型参数异常");
        }
        ContCheckInUserEntity contCheckInUserEntity = contCheckInUserService.getById(id);
        contCheckInUserEntity.setState(ApprovalStateEnum.REJECT.getValue());
        if ("20".equals(type)) {
            contCheckInUserEntity.setState(ApprovalStateEnum.REJECT.getValue());
        } else {
            contCheckInUserEntity.setType(CheckInUserTypeEnum.CHECKIN.getValue());
        }
        contCheckInUserService.updateById(contCheckInUserEntity);
        return RestResponse.success("审核不通过！");
    }

    @UoneLog("企业入住人申请搬离")
    @UonePermissions(LoginType.CUSTOM)
    @CacheLock(prefix = "applyForLeave", expire = 60)
    @RequestMapping(value = "/pass/applyForLeave", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public RestResponse applyForLeave(@RequestParam("id") String id, @RequestParam("contractId") String contractId, @RequestParam("contractSourceId") String contractSourceId) throws Exception {
        contCheckInUserService.passApplyRemoveById(id);
        return RestResponse.success();
    }

    // 申请搬离
    @UoneLog("入住人申请搬离")
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping(value = "/applyForLeave", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public RestResponse applyForLeave(@RequestParam("id") String id) throws Exception {
        ContCheckInUserEntity contCheckInUserEntity = contCheckInUserService.getById(id);
        String contractSourceId = contCheckInUserEntity.getContractSourceId();
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("state", ApprovalStateEnum.COMPLETE.getValue());
        map.put("type", CheckInUserTypeEnum.CHECKIN.getValue());
        map.put("contractSourceId", contractSourceId);
        //查找在住的人数, 最后一个人,不让申请搬离
        List<ContCheckInUserEntity> list = contCheckInUserService.getListByMap(map);
        if (CollectionUtils.isEmpty(list) || list.size() == 1) {
            return RestResponse.failure("您是最后一个入住人，不能申请搬离哦");
        }
        //获取入住人,更改为待搬离
        contCheckInUserEntity.setState(ApprovalStateEnum.APPROVAL.getValue());
        contCheckInUserEntity.setType(CheckInUserTypeEnum.REMOVE.getValue());
        contCheckInUserService.updateById(contCheckInUserEntity);

        ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(contractSourceId);
        // 消息推送
        sysPushMsgService.createPushMsg(MsgTypeEnum.BANLIRUZHURENSHENQING.getValue(), null, contContractSourceRel.getSourceId());
        return RestResponse.success();
    }

    //TODO modify by zengguoshen 20211105  start
    @RequestMapping(value = "/viewByContSourceId", method = RequestMethod.POST)
    public RestResponse viewByContSourceId(@RequestParam("id") String id) {
        List<ContCheckInUserEntity> users=contCheckInUserService.getByContractSourceId(id);
        List<ContCheckInUserVo> voList=new ArrayList<>();
        for(ContCheckInUserEntity checkInUserEntity:users){
            ContCheckInUserVo checkInUserVo = new ContCheckInUserVo();
            BeanUtil.copyProperties(checkInUserEntity, checkInUserVo);
            ContContractSourceRelEntity relEntity = contContractSourceRelService.getById(checkInUserEntity.getContractSourceId());
            //身份证
            List<SysFileEntity> idCardFiles = sysFileService.getListByFromIdAndType(checkInUserVo.getId(), SysFileTypeEnum.CHECKIN_ID_CARD);
            if (idCardFiles.size() <= 0) {
                idCardFiles = sysFileService.getListByFromIdAndType(relEntity.getContractId(), SysFileTypeEnum.ID_CARD);
            }
            checkInUserVo.setIdCardImgs(idCardFiles);
            //暂住证
            List<SysFileEntity> temporaryResidenceFiles = sysFileService.getListByFromIdAndType(checkInUserVo.getId(), SysFileTypeEnum.CHECKIN_TEMPORARY_RESIDENCE);
            if (temporaryResidenceFiles.size() <= 0) {

                temporaryResidenceFiles = sysFileService.getListByFromIdAndType(relEntity.getContractId(), SysFileTypeEnum.TEMPORARY_RESIDENCE);
            }
            checkInUserVo.setTemporaryResidenceImgs(temporaryResidenceFiles);
            voList.add(checkInUserVo);
        }
        return RestResponse.success().setData(voList);
    }
    //TODO modify by zengguoshen 20211105  end

    // 查看入住用户信息-XX公寓
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/view", method = RequestMethod.POST)
    public RestResponse view(@RequestParam("id") String id) {
        ContCheckInUserEntity checkInUserEntity = contCheckInUserService.getById(id);
        ContCheckInUserVo checkInUserVo = new ContCheckInUserVo();
        BeanUtil.copyProperties(checkInUserEntity, checkInUserVo);
        ContContractSourceRelEntity relEntity = contContractSourceRelService.getById(checkInUserEntity.getContractSourceId());
        //身份证
        List<SysFileEntity> idCardFiles = sysFileService.getListByFromIdAndType(checkInUserVo.getId(), SysFileTypeEnum.CHECKIN_ID_CARD);
        if (idCardFiles.size() <= 0) {
            idCardFiles = sysFileService.getListByFromIdAndType(relEntity.getContractId(), SysFileTypeEnum.ID_CARD);
        }
        checkInUserVo.setIdCardImgs(idCardFiles);
        //暂住证
        List<SysFileEntity> temporaryResidenceFiles = sysFileService.getListByFromIdAndType(checkInUserVo.getId(), SysFileTypeEnum.CHECKIN_TEMPORARY_RESIDENCE);
        if (temporaryResidenceFiles.size() <= 0) {

            temporaryResidenceFiles = sysFileService.getListByFromIdAndType(relEntity.getContractId(), SysFileTypeEnum.TEMPORARY_RESIDENCE);
        }
        checkInUserVo.setTemporaryResidenceImgs(temporaryResidenceFiles);
        //免冠照片
        List<SysFileEntity> portraitImages = sysFileService.getListByFromIdAndType(checkInUserVo.getId(), SysFileTypeEnum.BAREHEADED_PHOTO);
        checkInUserVo.setPortraitImages(portraitImages);
        return RestResponse.success().setData(checkInUserVo);
    }

    //删除同住人
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public RestResponse delete(@RequestParam("id") String id){
        boolean delete =contCheckInUserService.removeById(id);
        return RestResponse.success().setData(delete);

    }
    // 获取同住人信息-XX公寓
    @UonePermissions(value = LoginType.USER)
    @RequestMapping(value = "/getTzr", method = RequestMethod.POST)
    public RestResponse getTzr(@RequestParam("contractSourceId") String contractSourceId,
                               @RequestParam("renterId") String renterId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("contract_source_id", contractSourceId);
        wrapper.ne("renter_id", renterId);
        wrapper.eq("type", CheckInUserTypeEnum.CHECKIN.getValue());
        wrapper.eq("state", ApprovalStateEnum.APPROVAL.getValue());
        return RestResponse.success().setData(contCheckInUserService.list(wrapper));
    }

    // 根据合同id获得入住时间
    @UonePermissions
    @RequestMapping(value = "/getByContracId")
    public RestResponse getByContracId(@RequestParam("contractId") String contractId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("contract_id", contractId);
        ContContractSourceRelEntity csEntity=contContractSourceRelService.getByContractIdAndSourceId(contractId,null);
        List<ContCheckInUserEntity> list=contCheckInUserService.getByContractSourceId(csEntity.getId());
        if(ObjectUtil.isNull(list.get(0))){
            return RestResponse.success().setMessage("未办理入住");
        }
        return RestResponse.success().setData(list.get(0));
    }

    /**
     * 获取该企业下房间内同住人列表信息
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/pass/getRoomMate", method = RequestMethod.GET)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getRoomMate(HttpServletRequest request) throws Exception {
        // 通过 contractSource获取该合同下的同住人信息
        HashMap<String, Object> map = Maps.newHashMap();
        String name = request.getParameter("name");
        map.put("contractSourceId",request.getParameter("contractSourceId"));
        map.put("name",name);
        Page page = new Page();
        Long current = Long.parseLong(request.getParameter("current"));
        Long size = Long.parseLong(request.getParameter("size"));
        page.setCurrent(current);
        page.setSize(size);
        IPage<ContCheckInUserEntity> result = contCheckInUserService.selectRoomMate(page, map);
        return RestResponse.success().setData(result);
    }

    /**
     * 获取同住人（无合同场景）
     * 前端传入的房源id作为contract_source_id
     */
    @RequestMapping("/getCheckInUsersBySourceId")
    public RestResponse getCheckInUsersBySourceId(@RequestParam("sourceId")String sourceId,@RequestParam("isApplyCard")String isApplyCard){
        List<ContCheckInUserEntity> list = contCheckInUserService.getByContractSourceId(sourceId,isApplyCard);
        return RestResponse.success().setData(list);
    }

}
