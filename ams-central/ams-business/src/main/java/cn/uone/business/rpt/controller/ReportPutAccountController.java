package cn.uone.business.rpt.controller;


import cn.hutool.core.date.DateUtil;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.parameter.PutAccountPo;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.impl.ResProjectInfoServiceImpl;
import cn.uone.business.rpt.service.IReportPutAccountService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收费台账 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@RestController
@RequestMapping("/report/putAccount")
public class ReportPutAccountController extends BaseController {

    @Autowired
    private IReportPutAccountService putAccountService;

    @Autowired
    private IResProjectInfoService projectInfoService;

    @Autowired
    private IContContractSourceRelService contractSourceRelService;

    @RequestMapping("/generate")
    @UonePermissions
    public RestResponse generate(String mon) {
        putAccountService.generate(DateUtil.parseDate(mon+"-01"));
        return RestResponse.success();
    }

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, PutAccountPo param) {
        return RestResponse.success().setData(putAccountService.selectPages(page, param));
    }

    @RequestMapping(value = "/getPriceBySourceId", method = RequestMethod.GET)
    public RestResponse getPriceBySourceId(String sourceId,BigDecimal price) throws Exception {

//        ResSourceConfigureEntity sc = sourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", sourceId));
        if (null == price) {
            return RestResponse.failure("未查找到该合同租金");
        }
        ResProjectInfoServiceImpl.PriceVo orgFloat = projectInfoService.getFloatAttr(sourceId, true, PayTypeEnum.ONE_ONE.getValue());//机构浮动
        ResProjectInfoServiceImpl.PriceVo noOrgFloat = projectInfoService.getFloatAttr(sourceId, false, PayTypeEnum.ONE_ONE.getValue());//非机构浮动
        BigDecimal orgPrice = projectInfoService.getPrice(orgFloat.getFloatSize(), orgFloat.getFloatPrice(), price, BigDecimal.ZERO);//机构价格
        BigDecimal noOrgPrice = projectInfoService.getPrice(noOrgFloat.getFloatSize(), noOrgFloat.getFloatPrice(), price, BigDecimal.ZERO);//非机构价格
        Map<String, Object> map = Maps.newHashMap();
        map.put("orgPrice", orgPrice);
        map.put("noOrgPrice", noOrgPrice);
        map.put("price", price);
        return RestResponse.success().setData(map);
    }

    @RequestMapping(value = "/getOrdersByContractIdAndOrderType", method = RequestMethod.GET)
    public RestResponse getOrdersByContractIdAndOrderType(Page page, String contractId) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        List<String> types = Lists.newArrayList();
        types.add(OrderTypeEnum.RENT.getValue());
        types.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        types.add(OrderTypeEnum.CHECKOUTTUI.getValue());
        search.setOrderTypes(types);

        return RestResponse.success().setData(putAccountService.selectOrderPage(page, search));

    }

}
