package cn.uone.business.bil.controller;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.order.*;
import cn.uone.bean.entity.business.bil.*;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.tpi.unionPay.UnionPayConfigVo;
import cn.uone.bean.entity.tpi.unionPay.UnionPayVo;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.bil.service.*;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.service.impl.ContContractServiceImpl;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.tpi.UnionPayFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.flowable.engine.impl.persistence.entity.ResourceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("/bil/orderRefund")
public class BilOrderRefundController extends BaseController {

    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private ISysCompanyFegin sysCompanyFegin;
    @Autowired
    private IExpenseConfigFegin expenseConfigFegin;
    @Autowired
    private UnionPayFegin unionPayFegin;
    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;
    @Autowired
    private IBilRefundService bilRefundService;

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", UoneHeaderUtil.getProjectId());
        IPage<Map<String,Object>> pageList = bilOrderService.getToRefundOrderList(page,params);
        return RestResponse.success().setData(pageList);
    }

    /**
     * 线下确认退款
     * @param orderId
     * @param payTime
     * @param tradeCode
     * @param remark
     * @return
     */
    @RequestMapping("/offLineRefund")
    @Transactional
    public RestResponse offLineRefund(@RequestParam String orderId, Date payTime, String tradeCode, String remark) {
        try {
            BilOrderEntity entity = bilOrderService.getById(orderId);
            String payState = entity.getPayState();
            if(!payState.equals("60")){//存在待退款以外的其他状态,则设置状态检查为不通过
                return RestResponse.failure("该账单非待退款，清检查！");
            }
            entity.setActualPayment(entity.getPayablePayment());//更新实际支付金额
            entity.setPayState(PayStateEnum.REFUNDED.getValue());//更新支付状态
            entity.setTradeCode(tradeCode);//更新支付单号
            entity.setRemark(remark);
            entity.setPayTime(payTime);
            bilOrderService.updateById(entity);
            //保存退款记录
            BigDecimal payAmount = entity.getActualPayment().negate();
            String totalAmount = payAmount.multiply(new BigDecimal(100)).toString();
            BilRefundEntity bilRefundEntity = new BilRefundEntity();
            bilRefundEntity.setTkOrderId(orderId);
            bilRefundEntity.setSkOrderId(orderId);
            bilRefundEntity.setPayment(payAmount);
            bilRefundEntity.setSeqId(tradeCode);
            bilRefundEntity.setTotalAmount(totalAmount);
            bilRefundEntity.setRefundAmount(totalAmount);
            bilRefundEntity.setRefundStatus("SUCCESS");
            bilRefundEntity.setRefundOrderId(tradeCode);
            bilRefundEntity.setRefundTargetOrderId(tradeCode);
            bilRefundEntity.setRefundWay("2");
            bilRefundService.save(bilRefundEntity);
            return RestResponse.success().setMessage("提交成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    @RequestMapping("/onLineRefund")
    @Transactional
    public RestResponse onLineRefund(@RequestParam String orderId) {
        try {
            BilOrderEntity jzOrder = bilOrderService.getById(orderId);
            String payState = jzOrder.getPayState();
            if(!payState.equals("60")){//存在待退款以外的其他状态,则设置状态检查为不通过
                return RestResponse.failure("该账单非待退款，清检查！");
            }
            QueryWrapper query0 = new QueryWrapper<>();
            query0.eq("order_id", jzOrder.getId());
            query0.lt("payment", 0);
            query0.orderByDesc("payment");
            List<BilOrderItemEntity> jzItemList = bilOrderItemService.list(query0);
            //获取待结转明细
            BilOrderItemEntity yjfmItem = bilOrderItemService.getItemByOrderIdAndType(jzOrder.getId(),OrderItemTypeEnum.YAJINCONFISCATE.getValue());
            QueryWrapper query = new QueryWrapper<>();
            query.eq("order_id", jzOrder.getId());
            query.gt("payment", 0);
            query.notIn("order_item_type",OrderItemTypeEnum.YAJINCONFISCATE.getValue());
            List<BilOrderItemEntity> items = bilOrderItemService.list(query);
            String remark = null;
            for(BilOrderItemEntity jzItem : jzItemList) {
                BigDecimal carryOverPayment = jzItem.getPayment().negate();
                //如果存在押金罚没，则将押金退款结转到押金罚没
                if (yjfmItem != null && yjfmItem.getPayment().compareTo(BigDecimal.ZERO) == 1 && OrderItemTypeEnum.YAJINREFUND.getValue().equals(jzItem.getOrderItemType())) {
                    continue;
                }
                //结转其他费用
                if (items != null && items.size() > 0) {
                    boolean isContinue = false;
                    for (BilOrderItemEntity item : items) {
                        if (item.getPayment().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        carryOverPayment = carryOverPayment.subtract(item.getPayment());
                        if (carryOverPayment.compareTo(BigDecimal.ZERO) <= 0) {
                            item.setPayment(item.getPayment().subtract(carryOverPayment));
                            isContinue = true;
                            break;
                        } else {
                            item.setPayment(BigDecimal.ZERO);
                        }
                    }
                    if (isContinue) {
                        continue;
                    }
                }
                String orderType = null;
                String itemType = jzItem.getOrderItemType();
                if(OrderItemTypeEnum.RENT.getValue().equals(itemType)){
                    orderType = OrderTypeEnum.RENT.getValue();
                }else if(OrderItemTypeEnum.YAJINREFUND.getValue().equals(itemType)){
                    orderType = OrderTypeEnum.YAJIN.getValue();
                }else if(OrderItemTypeEnum.DEPOSITREFUND.getValue().equals(itemType)){
                    orderType = OrderTypeEnum.DEPOSITREFUND.getValue();
                }else if(OrderItemTypeEnum.SYNTHESIZE_BASEFEE.getValue().equals(itemType)){
                    orderType = OrderTypeEnum.SYNTHESIZE_BASEFEE.getValue();
                }
                QueryWrapper query1 = new QueryWrapper<>();
                query1.eq("payer_id", jzOrder.getPayerId());
                query1.ge("actual_payment", carryOverPayment);
                query1.eq("pay_way", PayWayEnum.WECHAT2.getValue());
                query1.eq("pay_state", PayStateEnum.PAYCONFIR.getValue());
                query1.eq("order_type", orderType);
                query1.orderByDesc("code");
                List<BilOrderEntity> orderList = bilOrderService.list(query1);
                if(orderList == null || orderList.size() == 0) {
                    return RestResponse.failure(StrUtil.format("不存在已在线支付的{}账单，清检查！",OrderTypeEnum.getNameByValue(orderType)));
                }
                BilOrderEntity toRefundOrder = orderList.get(0);
                ResSourceEntity resourceEntity = resSourceService.getById(toRefundOrder.getSourceId());
                ResProjectEntity project = resProjectService.getById(resourceEntity.getProjectId());
                JSONObject config = expenseConfigFegin.getExpenseConfigByCode(project.getCode(), ApiTypeEnum.UNION_PAY.getValue());
                UnionPayConfigVo unionPayConfig = JSONUtil.toBean(config,UnionPayConfigVo.class);
                UnionPayVo unionPayVo = new UnionPayVo();
                unionPayVo.setTotalAmount(carryOverPayment);
                unionPayVo.setMerOrderId(toRefundOrder.getMerOrderId());
                unionPayVo.setTargetOrderId(toRefundOrder.getTradeCode());
                unionPayVo.setUnionPayConfig(unionPayConfig);
                Map<String,String> map = unionPayFegin.refund(unionPayVo);
                String resp = map.get("resStr");
                System.out.println("返回结果:\n" + resp);
                net.sf.json.JSONObject respJson = net.sf.json.JSONObject.fromObject(resp);
                if (respJson.containsKey("targetStatus")) {
                    String status = respJson.getString("targetStatus");
                    bilInterfaceMsgService.addInterfaceMsg(map.get("requestMessage"), respJson.toString(), null, toRefundOrder.getMerOrderId(), "发起银联退款");
                    if ("SUCCESS|SUCCESS".equals(status)) {
                        jzOrder.setActualPayment(jzOrder.getPayablePayment());//更新实际支付金额
                        jzOrder.setPayState(PayStateEnum.REFUNDED.getValue());//更新支付状态
                        jzOrder.setTradeCode(respJson.getString("refundOrderId"));//更新支付单号
                        jzOrder.setRemark("银联在线退款");
                        jzOrder.setPayTime(new Date());
                        bilOrderService.updateById(jzOrder);
                        //保存退款记录
                        BilRefundEntity bilRefundEntity = new BilRefundEntity();
                        bilRefundEntity.setTkOrderId(orderId);
                        bilRefundEntity.setSkOrderId(toRefundOrder.getId());
                        bilRefundEntity.setPayment(carryOverPayment);
                        bilRefundEntity.setMerOrderId(toRefundOrder.getMerOrderId());
                        bilRefundEntity.setSeqId(respJson.getString("seqId"));
                        bilRefundEntity.setTotalAmount(respJson.getString("totalAmount"));
                        bilRefundEntity.setRefundAmount(respJson.getString("refundAmount"));
                        bilRefundEntity.setRefundStatus(respJson.getString("refundStatus"));
                        bilRefundEntity.setRefundOrderId(respJson.getString("refundOrderId"));
                        bilRefundEntity.setRefundTargetOrderId(respJson.getString("refundTargetOrderId"));
                        bilRefundEntity.setRefundWay("1");
                        bilRefundService.save(bilRefundEntity);
                    } else {
                        return RestResponse.failure("查询银联退款结果状态:" + status);
                    }
                } else {
                    Console.log("银联返回消息：{}",respJson);
                    return RestResponse.failure("银联返回消息：" + respJson.getString("errMsg")).setSuccess(false);
                }
            }
            return RestResponse.success().setMessage("提交成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }
}
