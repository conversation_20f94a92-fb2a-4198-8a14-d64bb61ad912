<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <properties>
        <!-- 文件输出格式 -->
        <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>

        <Property name="LOG_FILE_PATH">/Users/<USER>/IdeaProjects/rygy-dev/rygym/logs</Property>
    </properties>

    <appenders>
        <Console name="CONSOLE" target="system_out">
            <PatternLayout pattern="${PATTERN}"/>
        </Console>

        <RollingFile name="FileAppender" fileName="${LOG_FILE_PATH}/bussiness.log"
                     filePattern="${LOG_FILE_PATH}/bussiness-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout>
                <Pattern>${PATTERN}</Pattern>
            </PatternLayout>
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="4MB" />
                <TimeBasedTriggeringPolicy interval="1" />
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
<!--        <Socket name="socket" host="*************" port="9250">
            <JsonLayout properties="true"/>
        </Socket>-->
    </appenders>

    <loggers>
        <!--过滤掉一些无用的DEBUG信息-->
        <!--若是additivity设为false，则 子Logger 只会在自己的appender里输出，而不会在 父Logger 的appender里输出。-->
        <logger name="org.apache.commons.jexl2" level="error"/>
        <logger name="org.mybatis" level="error" additivity="false" />
        <logger name="org.springframework" level="error" additivity="false" />
        <logger name="springfox.documentation" level="error" additivity="false" />
        <logger name="com.alibaba.nacos.client" level="error" additivity="false" />
        <logger name="com.netflix.config" level="error" additivity="false" />

        <root level="info" >
            <appenderref ref="CONSOLE"/>
            <appenderref ref="FileAppender"/>
            <!--<appenderref ref="socket"/>-->
        </root>
<!--        <Logger name="org.springframework" level="info">
            <AppenderRef ref="socket"/>
        </Logger>-->
    </loggers>
</configuration>