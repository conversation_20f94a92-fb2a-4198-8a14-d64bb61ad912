server:
  port: 8032
  max-http-header-size: 10000000
  tomcat:
    max-http-post-size: 0
spring:
  application:
    name: ams-business
  profiles:
    active: prod
  jackson:
    default-property-inclusion: non_null
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: p,style,class
