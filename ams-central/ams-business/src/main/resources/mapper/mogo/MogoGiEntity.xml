<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.mogo.dao.MogoGiDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.mogo.MogoGiEntity">
        <result column="cityId" property="cityId" />
        <result column="cityName" property="cityName" />
        <result column="districtId" property="districtId" />
        <result column="districtName" property="districtName" />
        <result column="businessId" property="businessId" />
        <result column="businessName" property="businessName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        cityId, cityName, districtId, districtName, businessId, businessName
    </sql>
    <select id="getGi" resultType="cn.uone.bean.entity.business.mogo.MogoGiEntity">
        SELECT * FROM t_mogo_gi WHERE cityName LIKE
          CONCAT('%',#{cityName},'%')
          and districtName LIKE  CONCAT('%',#{districtName},'%')  limit 1
    </select>
</mapper>
