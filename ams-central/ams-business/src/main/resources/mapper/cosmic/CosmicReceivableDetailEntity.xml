<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cosmic.dao.CosmicReceivableDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cosmic.CosmicReceivableDetailEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="receivable_id" property="receivableId" />
        <result column="e_taxunitprice" property="eTaxunitprice" />
        <result column="taxrateid_number" property="taxrateidNumber" />
        <result column="e_tax" property="eTax" />
        <result column="e_unitprice" property="eUnitprice" />
        <result column="xmgd_ht_number" property="xmgdHtNumber" />
        <result column="xmgd_xm_number" property="xmgdXmNumber" />
        <result column="xmgd_ldfh_number" property="xmgdLdfhNumber" />
        <result column="xmgd_ytlx_number" property="xmgdYtlxNumber" />
        <result column="xmgd_lyfs_number" property="xmgdLyfsNumber" />
        <result column="xmgd_skqd_number" property="xmgdSkqdNumber" />
        <result column="e_expenseitem_number" property="eExpenseitemNumber" />
        <result column="e_remark" property="eRemark" />
        <result column="e_quantity" property="eQuantity" />
        <result column="xmgd_incometype_number" property="xmgdIncometypeNumber" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        receivable_id, e_taxunitprice, taxrateid_number, e_tax, e_unitprice, xmgd_ht_number, xmgd_xm_number, xmgd_ldfh_number, xmgd_ytlx_number, xmgd_lyfs_number, xmgd_skqd_number, e_expenseitem_number, e_remark, e_quantity, xmgd_incometype_number, remark
    </sql>

</mapper>
