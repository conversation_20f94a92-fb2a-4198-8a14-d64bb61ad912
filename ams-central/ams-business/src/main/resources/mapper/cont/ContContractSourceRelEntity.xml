<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cont.dao.ContContractSourceRelDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cont.ContContractSourceRelEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="source_id" property="sourceId"/>
        <result column="contract_id" property="contractId"/>
        <result column="car_id" property="carId"/>
        <result column="settle_state" property="settleState"/>
        <result column="orig_price" property="origPrice"/>
        <result column="cash_pledge" property="cashPledge"/>
        <result column="record_code" property="recordCode"/>
        <result column="record_state" property="recordState"/>
        <result column="record_id" property="recordId"/>
        <result column="refund_deposit" property="refundDeposit"/>
        <result column="fine_deposit" property="fineDeposit"/>
        <result column="float_size" property="floatSize"/>
        <result column="float_price" property="floatPrice"/>
        <result column="record_error_msg" property="recordErrorMsg"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        source_id,
        contract_id,
        car_id,settle_state,orig_price,refund_deposit,
        cash_pledge,float_size,float_price,record_code,record_id,record_state,record_error_msg,fine_deposit
    </sql>

    <sql id="Base_Alias_Column_List">
        t.id,
        t.source_id,
        t.contract_id,
        t.car_id,
        t.settle_state,
        t.orig_price,
        t.cash_pledge,t.float_size,t.float_price,t.record_code,t.record_id,t.record_state,t.record_error_msg,t.refund_deposit,t.fine_deposit
    </sql>

    <!-- 批量新增合同房源关系 -->
    <insert id="batchAdd" parameterType="java.util.List">
        INSERT INTO t_cont_contract_source_rel(
            id,
            create_by,
            create_date,
            update_by,
            update_date,
            source_id,
            contract_id,
            car_id,
            settle_state,
            orig_price,
        cash_pledge,float_size,float_price
        ) VALUES
            <foreach collection="list" item="item" index= "index" separator =",">
                (
                    #{item.id},
                    #{item.createBy},
                    #{item.createDate},
                    #{item.updateBy},
                    #{item.updateDate},
                    #{item.sourceId},
                    #{item.contractId},
                    #{item.carId},
                    #{item.settleState},
                    #{item.origPrice},
                #{item.cashPledge},
                #{item.floatSize},
                #{item.floatPrice}
                )
            </foreach>
    </insert>

    <!-- 查询房源信息 -->
    <select id="selectByVo"
            resultType="cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo">
        SELECT
        A.id,
        A.source_id,
        A.contract_id,
        A.car_id,
        A.settle_state,
        A.orig_price,
        A.cash_pledge,
        A.float_size,
        A.float_price,
        A.refund_deposit,
        B.project_id,
        B.partition_id,
        B.code,
        B.floor,
        B.is_talent,
        B.area,
        B.source_type,
        B.projectName,
        B.partitionName,
        B.source_name,
        B.is_checkIn AS isCheckIn,
        CH.examine AS examine,
        C.name AS userName,
        C.state AS checkInState,
        C.type AS checkInType,
        D.price,
        D.start_date,
        D.end_date,
        CONCAT(DATE_FORMAT(D.start_date, '%Y-%m-%d'), '~', DATE_FORMAT(D.end_date, '%Y-%m-%d')) AS dateTime,
        E.num
        FROM t_cont_contract_source_rel A
        LEFT JOIN v_res_source B ON A.source_id = B.id
        LEFT JOIN (select * from (select * from t_cont_check_in_user order by create_date desc) t group by
        contract_source_id) C ON A.id = C.contract_source_id
        LEFT JOIN (select * from (select * from t_cont_rent_ladder order by create_date) t group by contract_source_id)
        D ON A.id = D.contract_source_id
        LEFT JOIN (select * from (select * from t_base_car order by create_date desc) t group by contract_source_id) E
        ON A.id = E.contract_source_id
        LEFT JOIN t_cont_check_in_house CH on CH.contract_source_id = A.id
        WHERE 1 = 1
            <if test="map.contractId != null and map.contractId != ''">
                AND A.contract_id = #{map.contractId}
            </if>
            <if test="map.settleState != null and map.settleState != ''">
                AND A.settle_state = #{map.settleState}
            </if>
            <if test="map.sourceId != null and map.sourceId != ''">
                AND A.source_id = #{map.sourceId}
            </if>
        group by A.id
        order by d.start_date asc
    </select>

    <!-- 查询合同房源关系（关联租金表） -->
    <select id="query" resultType="cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo">
        SELECT
            A.id,
            A.source_id,
            A.contract_id,
            A.car_id,
            A.settle_state,
            A.orig_price,
            A.cash_pledge,
        A.float_size,A.float_price,A.refund_deposit,
            B.start_date,
            B.end_date,
            B.price
        FROM t_cont_contract_source_rel A
        LEFT JOIN t_cont_rent_ladder B ON A.id = B.contract_source_id
        WHERE A.contract_id = #{map.contractId}
            <if test="map.sourceId != null and map.sourceId != ''">
                AND A.source_id = #{map.sourceId}
            </if>
    </select>

    <!-- 员工入住 -->
    <select id="selectPageByStaffCheckIn"
            resultType="cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo">
        SELECT rel.id,rel.contract_id,(case when iu.type=20 and iu.state=2 then '' else u.name end)as name,u.renter_id as userId,
        s.projectName,s.partitionName,s.code,s.is_checkin AS isCheckIn,s.is_short isShort,s.source_type,
        ih.id AS checkInHouseId,iu.id AS checkInUserId,iu.state AS checkInState,iu.type AS checkInType
        from t_cont_contract_source_rel rel
        left join v_res_source s on rel.source_id = s.id
        left join (select * from (select * from t_cont_check_in_house where examine!='2' order by create_date desc) t
        group by
        contract_source_id) ih on rel.id = ih.contract_source_id
        left join (select * from (select * from t_cont_check_in_user where type = '10' or (type='20' and state != '2')
        order by create_date desc) t group by
        contract_source_id) iu on rel.id = iu.contract_source_id
        left join t_emp_user u ON ih.renter_id = u.renter_id and u.del_flag ='0' and (u.organization_id is null or u.organization_id=#{map.signerId})
        where exists (select 1 from t_cont_contract c
        where c.id = rel.contract_id and c.contract_type IN ('1', '3') and c.is_organize = '1' and c.state = '6' AND
        c.signer_id = #{map.signerId})
        <if test="map.projectName != null and map.projectName != ''">
            AND s.projectName LIKE CONCAT('%', #{map.projectName}, '%')
        </if>
        <if test="map.code != null and map.code != ''">
            AND s.code LIKE CONCAT('%', #{map.code}, '%')
        </if>
        <if test="map.name != null and map.name != ''">
            AND u.name LIKE CONCAT('%', #{map.name}, '%')
        </if>
        <if test="map.status != null and map.status != ''">
            AND s.is_checkin = #{map.status}
        </if>
        order by s.code
    </select>

    <!-- 查询房源信息 -->
    <select id="selectByContractId" resultType="cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo">
        SELECT
        A.id,
        A.source_id,
        A.cash_pledge,
        C.source_name,
        C.code,
        C.project_id,
        C.projectName,
        C.partitionName,
        C.area,
        C.house_type_id,
        C.floor,
        c.address projectAdd,
        d.pay_type,
        d.contract_code,
        d.start_date,
        d.end_date,
        trp.param_value tax,
        C.partition_id
        FROM t_cont_contract d,t_cont_contract_source_rel A
        LEFT JOIN v_res_source C ON A.source_id = C.id
        left join t_res_project_para trp on trp.project_id = c.project_id and trp.param_code = 'XMCS_0101'
        WHERE 1 = 1
        and a.contract_id = d.id
        AND A.contract_id = #{contractId}
    </select>

    <!-- 查询房源信息 -->
    <select id="selectRentByContractId" resultType="cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo">
        SELECT
        A.id,
        A.source_id,
        A.cash_pledge,
        B.price,
        B.start_date,
        B.end_date,
        C.source_name,
        C.code,
        C.project_id,
        C.projectName,
        C.partitionName,
        C.area,
        C.house_type_id,
        C.floor,
        c.address projectAdd,
        d.pay_type,
        d.contract_code
        FROM t_cont_contract d,t_cont_contract_source_rel A
        LEFT JOIN t_cont_rent_ladder B ON A.id = B.contract_source_id
        LEFT JOIN v_res_source C ON A.source_id = C.id
        WHERE 1 = 1
        and a.contract_id = d.id
        AND A.contract_id = #{contractId}
        order by B.start_date asc
    </select>

    <!-- 获取没有续租的合同房源信息 -->
    <select id="selectByIdAndNotRelet" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_cont_contract_source_rel cs
        where cs.id = #{id}
        and NOT EXISTS (select contract_id from t_biz_relet t where t.state != '30' and t.contract_id = cs.contract_id)
    </select>

    <!-- 获取没有换房的合同房源信息 -->
    <select id="selectByIdAndNotChange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_cont_contract_source_rel cs
        where cs.id = #{id}
        and NOT EXISTS (select contract_id from t_biz_change t where t.state != '30' and t.contract_id = cs.contract_id)

    </select>

    <!-- 获取某房源,合同处于某些状态的总记录条数 -->
    <select id="selectByContractStates" resultType="java.lang.Integer">
        select count(*) from t_cont_contract_source_rel cs LEFT JOIN t_cont_contract c on c.id = cs.contract_id
        where 1= 1
        and cs.source_id =#{sourceId}
        <if test="states != null and states.size() > 0" >
            and c.state in
            <foreach collection="states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT t.*,s.project_id projectid  from t_cont_contract_source_rel t,t_res_source s,t_res_plan_partition p
        WHERE t.source_id=s.id AND s.partition_id=p.id
        <if test="map.contractId != null and map.contractId != ''">
            AND	t.contract_id=#{map.contractId}
        </if>
        <if test="map.code != null and map.code != ''">
            AND s.code=#{map.code}
        </if>
        <if test="map.name != null and map.name != ''">
            AND p.name=#{map.name}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id=#{map.projectId}
        </if>
    </select>

    <select id="selectListByCondition" resultMap="BaseResultMap">
        SELECT t.*,s.project_id projectid  from t_cont_contract_source_rel t,t_res_source s,t_res_plan_partition p
        WHERE FIND_IN_SET(s.id,t.source_id) AND s.partition_id=p.id
        <if test="map.contractId != null and map.contractId != ''">
            AND	t.contract_id=#{map.contractId}
        </if>
        <if test="map.code != null and map.code != ''">
            AND s.code=#{map.code}
        </if>
        <if test="map.name != null and map.name != ''">
            AND p.name=#{map.name}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id=#{map.projectId}
        </if>
    </select>

    <select id="getUnCheckOut" resultMap="BaseResultMap">
        select cs.* from t_cont_contract_source_rel cs  left join t_res_source s on cs.source_id = s.id
        where 1=1 AND s.is_checkin = 1 AND cs.contract_id = #{contractId}
    </select>


</mapper>
