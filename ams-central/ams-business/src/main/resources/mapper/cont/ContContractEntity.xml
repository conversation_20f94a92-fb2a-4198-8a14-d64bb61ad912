<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cont.dao.ContContractDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cont.ContContractEntity">
        <result column="id" property="id"/>
        <result column="frame_contract_id" property="frameContractId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="contract_templet_id" property="contractTempletId"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_code" property="contractCode"/>
        <result column="signer_id" property="signerId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="sign_type" property="signType"/>
        <result column="sign_date" property="signDate"/>
        <result column="pay_type" property="payType"/>
        <result column="cost_configure_id" property="costConfigureId"/>
        <result column="rent_pay_payer" property="rentPayPayer"/>
        <result column="life_pay_payer" property="lifePayPayer"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="paper_code" property="paperCode"/>
        <result column="state" property="state"/>
        <result column="is_organize" property="isOrganize"/>
        <result column="is_agreement" property="isAgreement"/>
        <result column="is_pet" property="isPet"/>
        <result column="is_reservation" property="isReservation"/>
        <result column="cancel_remark" property="cancelRemark"/>
        <result column="remark" property="remark"/>
        <result column="platform" property="platform"/>
        <result column="platform_code" property="platformCode"/>
        <result column="sync_state" property="syncState"/>
        <result column="is_short" property="isShort"/>
        <result column="manager" property="manager"/>
        <result column="price" property="totalPrice"/>
        <result column="create_type" property="createType"/>
        <result column="gu_id" property="guId"/>
        <result column="fix_life_pay_payer" property="fixLifePayPayer"/>
        <result column="first_party" property="firstParty"/>
        <result column="rental_area" property="rentalArea"/>
        <result column="lease_use" property="leaseUse"/>
        <result column="electricity_deposit" property="electricityDeposit"/>
        <result column="plant_deposit" property="plantDeposit"/>
        <result column="other_lease" property="otherLease"/>
        <result column="price_strategy_id" property="priceStrategyId"/>
        <result column="tax_point" property="taxPoint"/>
        <result column="leasehold_area" property="leaseholdArea"/>
        <result column="free_start_date" property="freeStartDate"/>
        <result column="free_end_date" property="freeEndDate"/>
        <result column="old_renter_id" property="oldRenterId"/>
        <result column="year_increase" property="yearIncrease"/>
        <result column="year_num" property="yearNum"/>
        <result column="stop_time" property="stopTime"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="delivery_affirm_state" property="deliveryAffirmState"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="rent_type" property="rentType"/>
    </resultMap>

    <resultMap id="BaseCSResultMap" type="cn.uone.bean.entity.business.cont.ContContractEntity">
        <result column="id" property="id"/>
        <result column="frame_contract_id" property="frameContractId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="contract_templet_id" property="contractTempletId"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_code" property="contractCode"/>
        <result column="signer_id" property="signerId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="sign_type" property="signType"/>
        <result column="sign_date" property="signDate"/>
        <result column="pay_type" property="payType"/>
        <result column="cost_configure_id" property="costConfigureId"/>
        <result column="rent_pay_payer" property="rentPayPayer"/>
        <result column="life_pay_payer" property="lifePayPayer"/>
        <result column="fix_life_pay_payer" property="fixLifePayPayer"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="paper_code" property="paperCode"/>
        <result column="state" property="state"/>
        <result column="is_organize" property="isOrganize"/>
        <result column="is_agreement" property="isAgreement"/>
        <result column="is_pet" property="isPet"/>
        <result column="sync_state" property="syncState"/>
        <result column="is_reservation" property="isReservation"/>
        <result column="cancel_remark" property="cancelRemark"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="price" property="totalPrice"/>
        <result column="create_type" property="createType"/>
        <result column="price_strategy_id" property="priceStrategyId"/>
        <result column="old_renter_id" property="oldRenterId"/>
        <result column="year_increase" property="yearIncrease"/>
        <result column="year_num" property="yearNum"/>
        <result column="stop_time" property="stopTime"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="delivery_affirm_state" property="deliveryAffirmState"/>
        <result column="delivery_time" property="deliveryTime"/>
        <collection property="contractSource" ofType="cn.uone.bean.entity.business.cont.ContContractSourceRelEntity">
            <result column="cs.id" property="id"/>
            <result column="cs.create_by" property="createBy"/>
            <result column="cs.create_date" property="createDate"/>
            <result column="cs.update_by" property="updateBy"/>
            <result column="cs.update_date" property="updateDate"/>
            <result column="cs.source_id" property="sourceId"/>
            <result column="cs.contract_id" property="contractId"/>
            <result column="cs.car_id" property="carId"/>
            <result column="cs.settle_state" property="settleState"/>
            <result column="cs.orig_price" property="origPrice"/>
            <result column="cs.cash_pledge" property="cashPledge"/>
            <result column="cs.record_code" property="recordCode"/>
            <result column="cs.record_state" property="recordState"/>
            <result column="cs.record_id" property="recordId"/>
            <result column="cs.float_size" property="floatSize"/>
            <result column="cs.float_price" property="floatPrice"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="base">
        id,
        delivery_time,
        delivery_affirm_state,
        other_lease,
        first_party,
        rental_area,
        lease_use,
        electricity_deposit,
        plant_deposit,
        frame_contract_id,
        create_by,
        create_date,
        update_by,
        update_date,
        contract_templet_id,
        contract_type,
        contract_code,
        signer_id,
        start_date,
        end_date,
        sign_type,
        sign_date,
        pay_type,
        cost_configure_id,
        rent_pay_payer,
        life_pay_payer,
        fix_life_pay_payer,
        paper_code,
        state,
        is_organize,
        is_agreement,
        is_pet,
        is_reservation,
        cancel_remark,
        remark,
        platform,
        platform_code,
        sync_state,
        is_short,manager,
        price,create_type,gu_id,price_strategy_id,tax_point,leasehold_area,free_start_date,free_end_date,year_increase,year_num,stop_time,total_amount,rent_type
    </sql>

    <sql id="Base_Column_List">
        A.id,
        A.delivery_time,
        A.delivery_affirm_state,
        A.first_party,
        A.rental_area,
        A.lease_use,
        A.electricity_deposit,
        A.plant_deposit,
        A.other_lease,
        A.frame_contract_id,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.contract_templet_id,
        A.contract_type,
        A.contract_code,
        A.signer_id,
        A.start_date,
        DATE_FORMAT(A.end_date, '%Y-%m-%d') end_date,
        A.sign_type,
        A.sign_date,
        A.pay_type,
        A.cost_configure_id,
        A.rent_pay_payer,
        A.life_pay_payer,
        A.fix_life_pay_payer,
        A.paper_code,
        A.state,
        A.is_organize,
        A.platform,
        A.platform_code,
        A.sync_state,
        A.is_short,
        A.price,
        A.create_type,
        A.price_strategy_id,
        A.free_start_date,
        A.free_end_date,
        A.tax_point,
        A.leasehold_area,
        B.id as contractSourceId,
        C.source_name,
        C.id as source_id,
        C.source_type,
        C.id as sourceId,
        D.name,
        D.is_subsidy,
        R.id as releaseId,
        R.state as releaseState,
        R.approval_state as approvalState,
        R.checkout_date,
        R.apply_checkout_date,
        R.type AS releaseType,
        u.real_name managerName,
        A.year_increase,
        A.year_num,
        A.stop_time,
        A.remark,
        A.total_amount,
        A.rent_type
    </sql>

    <sql id="Base_Column_List_By_Organize">
    A.id,
    A.delivery_time,
    A.delivery_affirm_state,
    A.frame_contract_id,
    A.create_by,
    A.create_date,
    A.update_by,
    A.update_date,
    A.contract_templet_id,
    A.contract_type,
    A.contract_code,
    A.signer_id,
    A.start_date,
    A.end_date,
    A.sign_type,
    A.sign_date,
    A.pay_type,
    A.cost_configure_id,
    A.rent_pay_payer,
    A.life_pay_payer,
    A.fix_life_pay_payer,
    A.paper_code,
    A.state,
    A.is_organize,
    A.platform,
    A.platform_code,
    A.sync_state,
    A.price,
    A.first_party,
    A.rental_area,
    A.lease_use,
    A.electricity_deposit,
    A.plant_deposit,
    A.other_lease,
    A.create_type,
    A.price_strategy_id,
    B.id AS renterId,
    B.name AS renterName,
    c.source_name AS sourceName,
    E.id AS projectId,
    E.name AS projectName,
    R.id AS releaseId,
    R.state AS releaseState,
    R.type AS releaseType,
    if(-1 &lt; datediff(A.end_date,NOW())AND
    datediff(A.end_date, NOW())&lt; 31 and
    d.is_talent !='1' and
    A.state in('6','8'),IF(R.id is null,IF(A.contract_type !='2',1,0),0),0) AS relet
    </sql>

    <sql id="view">
        A.id,
        A.delivery_time,
        A.delivery_affirm_state,
        A.frame_contract_id,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.contract_templet_id,
        A.contract_type,
        A.contract_code,
        A.signer_id,
        A.start_date,
        A.end_date,
        A.sign_type,
        A.sign_date,
        A.pay_type,
        A.cost_configure_id,
        A.rent_pay_payer,
        A.life_pay_payer,
        A.fix_life_pay_payer,
        A.paper_code,
        A.state,
        A.is_organize,
        A.platform,
        A.platform_code,
        A.sync_state,
        A.price,
        A.create_type,
        A.price_strategy_id,
        B.name,
        B.id_type,
        B.id_no,
        B.legal_name,
        B.tax_org_name,
        B.tel,
        B.enterprise_address,
        B.enterprise_tel,
        B.enterprise_account,
        B.enterprise_bank,
        A.invoice_type,
        B.taxpayer_code,
        C.name AS renterName,
        D.name AS contractTempletName,
        E.name AS costConfigureName,
        E.project_id AS projectId,
        A.year_increase,
        A.year_num,
        A.rent_type
    </sql>

    <sql id="view2">
        A.id,
        A.delivery_time,
        A.delivery_affirm_state,
        A.frame_contract_id,
        A.contract_code,
        A.start_date,
        A.end_date,
        A.sign_date,
        A.paper_code,
        A.platform,
        A.platform_code,
        A.sync_state,
        A.price,
        A.create_type,
        A.price_strategy_id,
        B.name AS renterName,
        C.nature,
        C.bank_name,
        C.account,
        C.bank_deposit,
        C.bank_branch_id,
        C.check_out_time
    </sql>

    <sql id="view3">
        A.id,
        A.delivery_time,
        A.delivery_affirm_state,
        A.frame_contract_id,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.contract_templet_id,
        A.contract_type,
        A.contract_code,
        A.signer_id,
        A.start_date,
        A.end_date,
        A.sign_type,
        A.sign_date,
        A.pay_type,
        A.cost_configure_id,
        A.rent_pay_payer,
        A.life_pay_payer,
        A.fix_life_pay_payer,
        A.paper_code,
        A.state,
        A.is_organize,
        A.remark,
        A.platform,
        A.platform_code,
        A.sync_state,
        A.price,
        A.create_type,
        A.price_strategy_id,
        B.name,
        B.id_type,
        B.id_no,
        B.tel,
        C.cash_pledge,
        B.is_subsidy,
        B.subsidy_sum,
        B.special_item,B.special_agreement,B.purpose,B.business_scope,B.business_brand,
        B.address,B.urgenter,B.urgent_tel,
        C.id AS contractSourceId,
        D.name AS costConfigureName,
        D.project_id AS projectId,
        E.name AS contractTempletName,
        E.subsidy_price AS subsidyprice,
        C.source_id AS sourceId,
        F.source_name AS sourceName,
        F.code AS code,
        F.source_type,
        F.checkin_max,
        ifnull(H.pet_kept,'0') pet_kept,
        C.float_size AS houseRentFloat,
        I.min_rent_period minRentPeriod
    </sql>

    <sql id="Base_Column_CS_SQL">
        c.id,c.create_by,
        c.delivery_time,
        c.delivery_affirm_state,
        c.create_date,
        c.update_by,
        c.update_date,
        c.contract_templet_id,
        c.contract_type,
        c.contract_code,
        c.signer_id,
        c.start_date,
        c.end_date,
        c.sign_type,
        c.sign_date,
        c.pay_type,
        c.cost_configure_id,
        c.rent_pay_payer,
        c.life_pay_payer,
        c.fix_life_pay_payer,
        c.paper_code,
        c.state,
        c.is_organize,
        c.is_agreement,
        c.is_pet,
        c.is_reservation,
        c.cancel_remark,
        c.platform,
        c.platform_code,
        c.sync_state,
        c.invoice_type,
        c.price,
        c.create_type,
        cs.id AS "cs.id",
        cs.source_id AS "cs.source_id",
        cs.contract_id AS "cs.contract_id",
        cs.car_id AS "cs.car_id",
        cs.settle_state AS "cs.settle_state",
        cs.orig_price AS "cs.orig_price",
        cs.cash_pledge AS "cs.cash_pledge",cs.float_size AS "cs.float_size",cs.float_price AS "cs.float_price",cs.record_code AS "cs.record_code",cs.record_id AS "cs.record_id",cs.record_state AS "cs.record_state"
    </sql>

    <!-- 分页查询 -->
    <select id="queryContractList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List" />,C.is_checkin as isCheckIn,ch.renter_id as renterId,ch.examine as checkInState,
               ch.id as checkInHouseId ,D.tel,DATE_ADD(CURDATE(), INTERVAL 1 MONTH) &gt; A.end_date as relet,
               GROUP_CONCAT(C.source_name order by C.source_name asc) as code,B.cash_pledge as cashPledge,
               GROUP_CONCAT(C.partitionName order by C.partitionName asc) as partitionName,
               count(ciu.id) as checkInNum,D.id_no as idNo,
        SUM(CASE WHEN ab.order_type = '333' THEN ab.balance ELSE 0 END) AS waterBalance,
        SUM(CASE WHEN ab.order_type = '444' THEN ab.balance ELSE 0 END) AS electricityBalance
        FROM t_cont_contract A
        LEFT JOIN t_account_balance ab ON A.id = ab.contract_id
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_user ciu on ciu.contract_source_id = B.id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        <if test="map.isZzct == null or map.isZzct != 'zzct'">
        LEFT JOIN v_res_source C ON C.id = B.`source_id`
        </if>
        <if test="map.isZzct != null and map.isZzct == 'zzct'">
            LEFT JOIN v_res_source C ON find_in_set(C.id,B.`source_id`)>0
        </if>
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_biz_release R ON R.contract_id = A.id AND R.state!= '9'
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        LEFT JOIN v_sys_user u on u.id = A.manager
        WHERE 1 = 1 #project_datascope#
        AND C.project_id = #{map.projectId}
        <if test="map.sourceId != null and map.sourceId != ''">
            and C.id = #{map.sourceId}
        </if>
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%')
            OR A.paper_code LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND A.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.contractStates != null and map.contractStates !=''">
            AND  A.state in
            <foreach collection="map.contractStates.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.states != null and map.states !=''">
            AND  c.state in
            <foreach collection="map.states.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND A.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == '0'.toString()">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == '1'.toString()">
            AND A.platform = '1'
        </if>
        <if test="map.platform != null and map.platform == '3'.toString()">
            AND A.platform = '3'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.stopTimeStart != null">
            AND DATE_FORMAT(A.stop_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.stopTimeStart}, '%Y-%m-%d')
        </if>
        <if test="map.stopTimeEnd != null">
            AND DATE_FORMAT(A.stop_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.stopTimeEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
                C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
                or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
                or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
                or A.paper_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.ids != null and map.ids.size() > 0" >
            AND A.id in
            <foreach collection="map.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractSourceIds != null and map.contractSourceIds.size() > 0" >
            AND B.id in
            <foreach collection="map.contractSourceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY A.`id`
        <choose>
            <when test="map.orderName !=null and map.orderType != null ">
                <if test="map.orderName == 'code'">
                    order by C.source_name ${map.orderType}
                </if>
                <if test="map.orderName == 'signDate'">
                    order by A.sign_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'startDate'">
                    order by A.start_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'endDate'">
                    order by A.end_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'checkoutDate'">
                    order by R.checkout_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'taxPoint'">
                    order by A.tax_point  ${map.orderType}
                </if>
                <if test="map.orderName == 'leaseholdArea'">
                    order by A.leasehold_area  ${map.orderType}
                </if>
                <if test="map.orderName == 'partitionName'">
                    order by C.partitionName  ${map.orderType}
                </if>
            </when>
            <otherwise>
                order by c.source_name
            </otherwise>
        </choose>
    </select>


    <!-- 退转换续合同分页查询 -->
    <select id="releaseList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List" />,C.is_checkin as isCheckIn,ch.renter_id as renterId,ch.examine as checkInState,
        ch.id as checkInHouseId ,D.tel
        FROM t_biz_release R
        left join t_cont_contract A on R.contract_id = A.id
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        LEFT JOIN v_res_source C ON C.id in (B.source_id)
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        LEFT JOIN v_sys_user u on u.id = A.manager
        WHERE 1 = 1 #project_datascope#
        AND R.state != '9'
        <if test="map.type != null and map.type != ''">
            AND R.type = #{map.type}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and C.id = #{map.sourceId}
        </if>
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND C.project_id = #{map.projectId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND A.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.contractStates != null and map.contractStates !=''">
            AND  A.state in
            <foreach collection="map.contractStates.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.states != null and map.states !=''">
            AND  c.state in
            <foreach collection="map.states.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND A.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND A.platform = '1'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.ids != null and map.ids.size() > 0" >
            AND A.id in
            <foreach collection="map.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractSourceIds != null and map.contractSourceIds.size() > 0" >
            AND B.id in
            <foreach collection="map.contractSourceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by R.id
        <choose>
            <when test="map.orderName !=null and map.orderType != null ">
                <if test="map.orderName == 'sourceName'">
                    order by C.source_name ${map.orderType}
                </if>
                <if test="map.orderName == 'signDate'">
                    order by A.sign_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'startDate'">
                    order by A.start_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'endDate'">
                    order by A.end_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'checkoutDate'">
                    order by R.checkout_date  ${map.orderType}
                </if>
            </when>
            <otherwise>
                order by c.source_name
            </otherwise>
        </choose>
    </select>


    <select id="releaseWaitRenterConfirmList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        <include refid="Base_Column_List"/>
        ,C.is_checkin as isCheckIn,ch.renter_id as renterId,ch.examine as checkInState,
        ch.id as checkInHouseId ,D.tel
        FROM t_biz_release R
        LEFT JOIN t_cont_contract A on R.contract_id = A.id
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id, B.source_id )
        LEFT JOIN v_res_project P ON P.ID = C.project_id
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        LEFT JOIN v_sys_user u on u.id = A.manager
        WHERE
        R.state != 9
        AND A.state = #{map.contractState}
        <if test="map.type != null and map.type != ''">
            AND R.type = #{map.type}
        </if>
        <if test="map.ids != null and map.ids.size() > 0">
            AND A.id in
            <foreach collection="map.ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.projectCodes!= null and map.projectCodes.size()>0">
            AND P.code IN
            <foreach collection="map.projectCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <!-- 分页查询 -->
    <select id="getRoomMateList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        select c.contract_code , s.source_name sourceName, IF(ctc.renter_id is null ,'同住人','签约人') roomMate
         ,ctc.`name` name, ctc.tel , v.id_no idNo
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        left join t_cont_check_in_user ctc on ctc.contract_source_id = B.id
        LEFT JOIN v_res_source C ON find_in_set(C.id,B.source_id) &gt; 0
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN v_sys_renter v  on v.id = A.signer_id
        WHERE 1 = 1 and A.state in ('5','6') #project_datascope#
        AND C.project_id = #{map.projectId}
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND A.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.states != null and map.states !=''">
            AND  c.state in
            <foreach collection="map.states.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND A.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND A.platform = '1'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.ids != null and map.ids.size() > 0" >
            AND A.id in
            <foreach collection="map.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractSourceIds != null and map.contractSourceIds.size() > 0" >
            AND B.id in
            <foreach collection="map.contractSourceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY A.id
        <choose>
            <when test="map.orderName !=null and map.orderType != null ">
                <if test="map.orderName == 'sourceName'">
                    order by C.source_name ${map.orderType}
                </if>
                <if test="map.orderName == 'signDate'">
                    order by A.sign_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'startDate'">
                    order by A.start_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'endDate'">
                    order by A.end_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'checkoutDate'">
                    order by R.checkout_date  ${map.orderType}
                </if>
            </when>
            <otherwise>
                order by c.source_name
            </otherwise>
        </choose>
    </select>



    <!-- 分页查询 -->
    <select id="exportContractList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List" />,C.is_checkin as isCheckIn,ch.renter_id as renterId,ch.examine as checkInState,
        ch.id as checkInHouseId ,D.tel , tsd.discount discount ,rsc.low_price lowPrice, rsc.price originalPrice,
         B.cash_pledge rentalPrice ,C.area roomArea ,DATE_FORMAT(R.checkout_date,'%Y-%m-%d') checkoutDateStr,B.cash_pledge cashPledge,
        GROUP_CONCAT(C.source_name) as code
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
        left join t_res_source_configure rsc on rsc.source_id = C.id
        left join (select * from t_sale_demand tsm where tsm.is_fix ='1' limit 1) tsd on tsd.source_id = C.id
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_biz_release R ON R.contract_id = A.id AND R.state!= '9'
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        LEFT JOIN v_sys_user u on u.id = A.manager
        WHERE 1 = 1 #project_datascope#
        AND C.project_id = #{map.projectId}
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND A.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.states != null and map.states !=''">
            AND  c.state in
            <foreach collection="map.states.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND A.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND A.platform = '1'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.ids != null and map.ids.size() > 0" >
            AND A.id in
            <foreach collection="map.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractSourceIds != null and map.contractSourceIds.size() > 0" >
            AND B.id in
            <foreach collection="map.contractSourceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by A.id
        <choose>
            <when test="map.orderName !=null and map.orderType != null ">
                <if test="map.orderName == 'sourceName'">
                    order by C.source_name ${map.orderType}
                </if>
                <if test="map.orderName == 'signDate'">
                    order by A.sign_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'startDate'">
                    order by A.start_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'endDate'">
                    order by A.end_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'checkoutDate'">
                    order by R.checkout_date  ${map.orderType}
                </if>
            </when>
            <otherwise>
                order by c.source_name
            </otherwise>
        </choose>
    </select>


    <select id="queryContractPage" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List" />
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_biz_release R ON R.contract_id = A.id AND R.state!= '9'
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        left join v_sys_user u on u.id = A.manager
        WHERE 1 = 1
        <if test="map.projectId != null and map.projectId != ''">
            AND C.project_id = #{map.projectId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.key != null and map.key != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.key}, '%') OR C.code LIKE CONCAT('%', #{map.key}, '%'))
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.states != null and map.states !=''">
            AND  c.state in
            <foreach collection="map.states.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.frameContractId != null and map.frameContractId != ''">
            AND A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.signerId != null and map.signerId != ''">
            AND A.signer_id = #{map.signerId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND A.platform = '1'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.ids != null and map.ids.size() > 0" >
            AND A.id in
            <foreach collection="map.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractSourceIds != null and map.contractSourceIds.size() > 0" >
            AND B.id in
            <foreach collection="map.contractSourceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY A.id
        <choose>
            <when test="map.orderName !=null and map.orderType != null ">
                <if test="map.orderName == 'sourceName'">
                    order by C.source_name ${map.orderType}
                </if>
                <if test="map.orderName == 'signDate'">
                    order by A.sign_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'startDate'">
                    order by A.start_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'endDate'">
                    order by A.end_date  ${map.orderType}
                </if>
                <if test="map.orderName == 'checkoutDate'">
                    order by R.checkout_date  ${map.orderType}
                </if>
            </when>
            <otherwise>
                order by C.code  desc
            </otherwise>
        </choose>
    </select>

    <!-- 查询机构合同（分页、项目权限） -->
    <select id="selectByOrganize" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List_By_Organize" />
        FROM t_cont_contract A
        INNER JOIN v_sys_renter B ON A.signer_id = B.id
        LEFT JOIN t_cont_contract_source_rel C ON A.id = C.contract_id
        LEFT JOIN t_res_source D ON C.source_id = D.id
        LEFT JOIN t_res_project E ON D.project_id = E.id
        LEFT JOIN t_biz_release R ON R.contract_id = A.id and R.state !='9'
        WHERE 1 = 1 #project_datascope#
        AND A.is_organize = '1'
        AND E.id = #{map.projectId}
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR B.name LIKE CONCAT('%', #{map.antistop},
            '%'))
        </if>
        <if test="map.id != null and map.id != ''">
            AND A.id = #{id}
        </if>
        <if test="map.isFrame=='1'.toString()">
            and A.frame_contract_id is not null
        </if>
        <if test="map.isFrame=='0'.toString()">
            and A.frame_contract_id is null
        </if>
        GROUP BY A.id
        order BY A.create_date desc,sign_date desc
    </select>


    <!-- 根据签约用户id查询合同 -->
    <select id="selectBySignerId" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        c.id,i.name,i.tel,c.signer_id as signerId,c.contract_code contractCode,c.platform,c.platform_code,
        s.source_name sourceName,cs.id 'value',s.source_name name,c.end_date,c.contract_type,s.source_type,s.project_id
        FROM t_cont_contract c
        LEFT JOIN t_cont_contract_info i ON i.contract_id=c.id
        LEFT JOIN t_cont_contract_source_rel cs ON cs.contract_id=c.id
        LEFT JOIN v_res_source s ON FIND_IN_SET(s.id,cs.source_id) > 0
        WHERE  s.state = 2
        and c.is_short ='0'
        and signer_id = #{map.signerId}
        <if test="map.organize != null and map.organize != ''">
            AND c.is_organize = #{map.organize}
        </if>
        <if test="map.state != null and map.state != ''">
            AND c.state = #{map.state}
        </if>
        <if test="map.contractStates != null and map.contractStates !=''">
            AND  c.state in
            <foreach collection="map.contractStates.split(',')" index="index" item="item" open="(" separator=","  close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND c.contract_type = #{map.contractType}
        </if>
        <if test="map.contractTypes != null and map.contractTypes != ''">
            AND c.contract_type IN #{map.contractTypes}
        </if>
        <if test="map.contractTypeList != null and map.contractTypeList .size() > 0">
            AND c.contract_type IN
            <foreach collection="map.contractTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.sourceType != null and map.sourceType != ''">
            AND s.source_type = #{map.sourceType}
        </if>
        <if test="map.isTalent != null and map.isTalent == 0">
            AND (s.is_talent = 0 or s.is_talent is null or s.is_talent ='')
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (c.platform = '0' or c.platform is null or c.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND c.platform = '1'
        </if>
        <if test="map.isOrganze != null and map.isOrganze != ''">
            AND c.is_organize = #{map.isOrganze}
        </if>
        <!--是否在30天内过期,即合同截止时间在未来30天之内-->
        <if test="map.lastMonth != null and map.lastMonth != ''">
            AND date(DATE_FORMAT(c.end_date,'%Y-%m-%d')) <![CDATA[ <= ]]> DATE_SUB(CURDATE(), INTERVAL -30 DAY)
            AND date(DATE_FORMAT(c.end_date,'%Y-%m-%d')) <![CDATA[ >= ]]> CURDATE()
        </if>
        <!--如果租客的某个可续租房源不等于"已取消"，此时又没有其他可续租的房源-->
        <if test="map.notInRelet != null and map.notInRelet != ''">
            AND NOT EXISTS (select contract_id from t_biz_check_out t where t.state!='4' and t.contract_id = c.id)
            and NOT EXISTS (select contract_id from t_biz_change t where t.state != '30' and t.contract_id = c.id)
            and NOT EXISTS (select contract_id from t_biz_relet A WHERE A.contract_id = c.id and A.state !='30')
        </if>
        <!--如果租客的某个可续租房源不等于"已取消"，此时又没有其他可续租的房源-->
        <!--申请换房的时候，选择原房源信息，应该要过滤掉房源当前有续租合同起始日期大于当前日期-->
        <if test="map.notInChange != null and map.notInChange != ''">
            and NOT EXISTS (select contract_id from t_biz_change t where t.state != '30' and t.contract_id = c.id)
            and NOT EXISTS (select contract_id from t_biz_relet A left join t_cont_contract t on A.new_contract_id =t.id
            WHERE c.id = A.contract_id and A.state != '30' and DATE_FORMAT(ifnull(t.start_date,now()),'%Y-%m-%d') &gt;=
            DATE_FORMAT(now(),'%Y-%m-%d') and DATE_FORMAT(ifnull(t.end_date,now()),'%Y-%m-%d') &lt;=
            DATE_FORMAT(now(),'%Y-%m-%d'))
            AND c.id NOT in (
            SELECT
            contract_id
            FROM
            t_biz_relet A
            LEFT JOIN t_cont_contract t ON A.new_contract_id = t.id
            Left join t_cont_contract t1 on A.contract_id = t1.id
            WHERE t.state in ('1','9') and t1.state ='6' and c.id = A.contract_id
            )
        </if>
        <if test="map.inTime != null and map.inTime != ''">
            AND date(DATE_FORMAT(c.start_date,'%Y-%m-%d')) &lt;= date(DATE_FORMAT(#{map.inTime},'%Y-%m-%d'))
            AND date(DATE_FORMAT(c.end_date,'%Y-%m-%d')) >= date(DATE_FORMAT(#{map.inTime},'%Y-%m-%d'))
        </if>
        <if test="map.isChange != null and map.isChange == 1">
            AND date(DATE_FORMAT(c.start_date,'%Y-%m-%d')) <![CDATA[ <= ]]> CURDATE()
            AND date(DATE_FORMAT(c.end_date,'%Y-%m-%d')) <![CDATA[ > ]]> CURDATE()
        </if>
        group by c.id
        ORDER BY c.create_date DESC
    </select>

    <!-- 获取三位数 -->
    <select id="getNum" resultType="java.lang.Integer">
        SELECT CAST(LPAD(IFNULL(MAX(SUBSTRING(paper_code, -3)), 0) + 1, 3, 0) AS CHAR) NUM
        FROM t_cont_contract
        WHERE 1 = 1
        <if test="param != null and param != ''">
            AND paper_code LIKE CONCAT('%', #{param}, '%')
            AND paper_code LIKE '%雅园*公寓%'
        </if>
    </select>

    <!-- 查看合同 -->
    <select id="getContractById" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="view" />
        FROM t_cont_contract A
        LEFT JOIN t_base_enterprise B ON A.signer_id = B.renter_id
        LEFT JOIN v_sys_renter C ON A.signer_id = C.id
        LEFT JOIN t_cont_temp D ON A.contract_templet_id = D.id
        LEFT JOIN t_res_cost_configure E ON A.cost_configure_id = E.id
        WHERE 1 = 1
        <if test="map.id != null and map.id != ''">
            AND A.id = #{map.id}
        </if>
    </select>

    <!-- 查看合同（机构合同退租） -->
    <select id="getContractByCheckOut" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="view2" />
        FROM t_cont_contract A
        LEFT JOIN v_sys_renter B ON A.signer_id = B.id
        LEFT JOIN t_biz_check_out C ON A.id = C.contract_id
        WHERE 1 = 1
        <if test="map.id != null and map.id != ''">
            AND A.id = #{map.id}
        </if>
        GROUP BY A.id
    </select>

    <select id="countContractNum" resultType="cn.uone.bean.entity.business.cont.vo.ContractCountVo">
        select ifnull(sum(if(c.sign_date is not null,1,0)),0) totalTimeNum,
        ifnull(sum(if(DATE_FORMAT(now(),'%Y-%m-%d') = DATE_FORMAT(c.sign_date,'%Y-%m-%d'),1,0)),0) todayNum,
        ifnull(sum(if(DATE_FORMAT(now(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m'),1,0)),0) thisMonNum,
        ifnull(sum( IF ( c.state = '1' and c.is_organize='0', 1, 0 ) ),0) tobeNum,
        ifnull(sum( IF ( c.state = '9' and c.is_organize='0', 1, 0 ) ),0) waitNum,
        ifnull(sum(if((datediff(c.end_date,now()) &lt; 30 and now()-c.end_date &lt; 0),1,0)),0) tobeExpired,
        ifnull(sum(if((datediff(c.end_date,now()) &lt; 1 and now()-c.end_date &lt; 0) and c.state in('6','8','12','13','14'),1,0)),0) expiredNow
        from t_cont_contract c
        where exists (select 1 from t_cont_contract_source_rel tc,t_res_source s
            where FIND_IN_SET(s.id,tc.source_id) > 0 and tc.contract_id = c.id   #project_datascope# #zone_datascope#
        )
    </select>

    <select id="getStatistics" resultType="cn.uone.bean.entity.business.cont.vo.ContractCountVo">
        select
        IFNULL(SUM(IF(c.`platform`='0' AND c.state IN ('5','6','17','8','12','18','13','14','15','19','20','16'),1,0)),0)
        shSignNum,
        IFNULL(SUM(IF(c.`platform`='3' AND c.state IN ('5','6','17','8','12','18','13','14','15','19','20','16'),1,0)),0)
        ygSignNum,
        IFNULL(SUM(IF(c.state = '6',1,0)),0) totalEffectedNum,
        IFNULL(SUM(IF(c.`platform`='0' AND c.state in('6','17','8','12','18','13','14','15','19','20','16'),1,0)),0) shEffectedNum,
        IFNULL(SUM(IF(c.`platform`='3' AND c.state in('6','17','8','12','18','13','14','15','19','20','16'),1,0)),0) ygEffectedNum,
        IFNULL(SUM(IF(c.state = '5',1,0)),0) totalToEffectNum,
        IFNULL(SUM(IF(c.`platform`='0' AND c.state ='5',1,0)),0) shToEffectNum,
        IFNULL(SUM(IF(c.`platform`='3' AND c.state ='5',1,0)),0) ygToEffectNum,
        IFNULL(SUM(IF(c.state in('17','8','13'),1,0)),0) totalOuttingNum,
        IFNULL(SUM(IF(c.`platform`='0' AND c.state in('17','8','13'),1,0)),0) shOuttingNum,
        IFNULL(SUM(IF(c.`platform`='3' AND c.state in('17','8','13'),1,0)),0) ygOuttingNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(c.sign_date,'%Y-%m-%d')) AND (c.state='6' OR c.state='5'),1,0)),0) todayNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(c.sign_date,'%Y-%m-%d')) AND c.`platform`='0' AND c.state IN ('5','6'),1,0)),0) todayShSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(c.sign_date,'%Y-%m-%d')) AND c.`platform`='3' AND c.state IN ('5','6'),1,0)),0) todayYgSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.state !='3',1,0)),0) thisMonNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.`platform`='0' AND c.state !='3',1,0)),0) monShSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.`platform`='3' AND c.state !='3',1,0)),0) monYgSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.state !='3',1,0)),0) thisYearNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.`platform`='0' AND c.state !='3',1,0)),0) yearShSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.`platform`='3' AND c.state !='3',1,0)),0) yearYgSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.state ='6',1,0)),0) monEffectedNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.`platform`='0' AND c.state ='6',1,0)),0) monShEffectedNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(c.sign_date,'%Y-%m')) AND c.`platform`='3' AND c.state ='6',1,0)),0) monYgEffectedSignNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.state ='6',1,0)),0) yearEffectedNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.`platform`='0' AND c.state ='6',1,0)),0) yearShEffectedNum,
        IFNULL(SUM(IF((DATE_FORMAT(NOW(),'%Y') = DATE_FORMAT(c.sign_date,'%Y')) AND c.`platform`='3' AND c.state ='6',1,0)),0) yearYgEffectedNum,
        ifnull(sum(if((c.state in('5','6')),1,0)),0) totalTimeNum,
        ifnull(sum( IF ( c.state = '1' and c.is_organize='0', 1, 0 ) ),0) tobeNum,
        ifnull(sum( IF ( c.state = '9' and c.is_organize='0', 1, 0 ) ),0) waitNum,
        ifnull(sum(if((DATE_FORMAT(now(),'%Y-%m-%d') = DATE_FORMAT(c.sign_date,'%Y-%m-%d')) and c.create_type='0',1,0)),0) todayRenewal,
        ifnull(sum(if((datediff(c.end_date,now()) &lt; 30 and now()-c.end_date &lt; 0) and c.state in('6','8','12','13','14'),1,0)),0) tobeExpired,
        ifnull(sum(if((datediff(c.end_date,now()) &lt; 1 and now()-c.end_date &lt; 0) and c.state = '6',1,0)),0) expiredNow
        from t_cont_contract c
        where 1=1 AND c.`contract_type`='1'
        and EXISTS (
        select 1 from (select ct.contract_id ,ts.project_id from t_cont_contract_source_rel ct , t_res_source ts where 1=1 and ts.id in(ct.source_id)) gt
        where gt.contract_id = c.id
        <if test="map.projectId != null and map.projectId != '' ">
            and gt.project_id =#{map.projectId}
        </if>
        )
    </select>
    <select id="inReviewContByHomePage" resultType="java.lang.String">
        select
        c.id
        from t_cont_contract c
        where 1=1 AND c.`contract_type`='1'
        and EXISTS (
        select 1 from (select ct.contract_id ,ts.project_id from t_cont_contract_source_rel ct , t_res_source ts where 1=1 and ts.id in(ct.source_id)) gt
        where gt.contract_id = c.id   and gt.project_id = #{projectId} )
        and c.state = '1'
         and c.is_organize = '0'
    </select>

    <select id="getExpireByHomePage" resultType="java.lang.String">
        select
        c.id
        from t_cont_contract c
        where 1=1 AND c.`contract_type`='1'
        and c.state = '6'
        and datediff(c.end_date,now()) &lt; 1 and now()-c.end_date &lt; 0
        and EXISTS (
        select 1 from (select ct.contract_id ,ts.project_id from t_cont_contract_source_rel ct , t_res_source ts where 1=1 and ts.id in(ct.source_id)) gt
        where gt.contract_id = c.id   and gt.project_id = #{projectId} )
        and c.state = '1'
        and c.is_organize = '0'


    </select>

    <!-- 获取三位数 -->
    <select id="getAvgRent" resultType="java.lang.Double">
        select IFNULL(ROUND(avg(ct.cash_pledge),2),0) avgPayment from t_cont_contract_source_rel ct , t_cont_contract c , v_res_source s
            where c.id = ct.contract_id and s.id = ct.source_id and c.state in('6','5')
        <if test="projectId != null and projectId != '' ">
            and s.project_id = #{projectId}
        </if>
    </select>

    <!-- 获取合同备案数 -->
    <select id="getRecordNum" resultType="java.lang.Integer">
        select count(1) from t_cont_contract c , t_cont_contract_source_rel ct , v_res_source s
        where FIND_IN_SET(s.id,ct.source_id) > 0 and ct.contract_id = c.id and ct.record_state = '6'
        <if test="projectId != null and projectId != '' ">
            and s.project_id = #{projectId}
        </if>
    </select>


    <!-- 查看合同 -->
    <select id="selectContractById" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        <include refid="view3"/>
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_info B ON A.id = B.contract_id
        LEFT JOIN t_cont_contract_source_rel C ON A.id = C.contract_id
        LEFT JOIN t_res_cost_configure D ON A.cost_configure_id = D.id
        LEFT JOIN t_cont_temp E ON A.contract_templet_id = E.id
        inner JOIN v_res_source F ON FIND_IN_SET(F.id,C.source_id) > 0
        left join t_res_source_configure H on h.source_id = F.id
        LEFT JOIN t_res_project_info I ON I.project_id = F.project_id
        WHERE 1 = 1
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
        group by a.id
    </select>

    <!-- 合同备案（合同相关信息） -->
    <select id="getContractInfoById" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        A.id, A.delivery_time, A.delivery_affirm_state, A.frame_contract_id,
        A.create_by, A.create_date, A.update_by, A.update_date,
        A.contract_templet_id, A.contract_type, A.contract_code, A.signer_id,
        A.start_date, A.end_date, A.sign_type, A.sign_date,
        A.pay_type, A.cost_configure_id, A.rent_pay_payer, A.life_pay_payer,
        A.fix_life_pay_payer, A.paper_code, A.state, A.is_organize,
        A.remark, A.platform, A.platform_code, A.sync_state,
        l.price, A.create_type, A.price_strategy_id, B.name,
        B.id_type, B.id_no, B.tel, C.cash_pledge,
        B.is_subsidy, B.subsidy_sum, B.special_item, B.special_agreement,
        B.purpose, B.business_scope, B.business_brand, B.address,
        B.urgenter, B.urgent_tel, C.id AS contractSourceId, D.name AS costConfigureName,
        D.project_id AS projectId, E.name AS contractTempletName, E.subsidy_price AS subsidyprice, C.source_id AS sourceId,
        F.source_name AS sourceName, F.code AS code, F.source_type, F.checkin_max,F.`partitionName`,
        ifnull(H.pet_kept,'0') pet_kept, C.float_size AS houseRentFloat, I.min_rent_period minRentPeriod,
        F.degree,ht.room,ht.hall,ht.toilet,if(ht.kitchen is null,0,1) kitchen,F.area AS roomArea,F.partition_id partitionId
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_info B ON A.id = B.contract_id
        LEFT JOIN t_cont_contract_source_rel C ON A.id = C.contract_id
        LEFT JOIN t_cont_rent_ladder l on l.contract_source_id= C.id
        LEFT JOIN t_res_cost_configure D ON A.cost_configure_id = D.id
        LEFT JOIN t_cont_temp E ON A.contract_templet_id = E.id
        inner JOIN v_res_source F ON FIND_IN_SET(F.id,C.source_id) > 0
        LEFT JOIN t_res_house_type ht ON F.house_type_id = ht.id
        left join t_res_source_configure H on h.source_id = F.id
        LEFT JOIN t_res_project_info I ON I.project_id = F.project_id
        WHERE 1 = 1
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
        group by a.id
    </select>


    <!--  查询已生效合同  -->
    <select id="getNameAndValue" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
    SELECT
            A.id,
            A.state,
            A.contract_type,
            A.start_date,
            A.end_date,
            A.is_organize,
            A.is_agreement,
            if(C.is_checkin='1',C.is_checkin,A.is_reservation) is_reservation,
            A.cancel_remark,
            A.platform,
            A.platform_code,
            A.signer_id as renterId,
            A.price as price,
            B.id AS contractSourceId,
            B.source_id,
            C.source_name,
            C.project_id,
            C.source_type,
            C.is_checkIn,
            D.url themeUrl,
            E.url as url,
            A.create_date,
            p.id as projectId,
            p.name as projectName,
            RE.id as releaseId
        FROM
            t_cont_contract A
            LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
            LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
            LEFT JOIN t_res_project p ON p.id = c.project_id
            LEFT JOIN v_sys_renter r ON r.id= A.signer_id
            LEFT JOIN ( SELECT * FROM t_biz_release  where type='0') RE ON RE.contract_id=A.id
            LEFT JOIN ( SELECT * FROM t_sys_file GROUP BY from_id ) D ON C.theme_id = D.from_id
            LEFT JOIN ( SELECT * FROM t_sys_file WHERE type='15' GROUP BY from_id ) E ON C.house_type_id = E.from_id
        WHERE
            if(A.is_organize = '0' and A.state IS NOT NULL,1,if(A.is_organize = '1' and A.state IN ( '6', '7', '8' ),1,0)) = 1
            AND A.signer_id = #{renterId} and (A.is_short is null or A.is_short ='0') and A.state IN ('6','14')
            AND A.contract_type != '6'
            GROUP BY A.id
            ORDER BY A.create_date desc
    </select>

    <!-- 查询合同分页-XX公寓 -->
    <select id="selectPageByComun" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
            A.id,
            A.delivery_affirm_state,
            A.state,
            A.contract_type,
            A.start_date,
            A.end_date,
            A.is_organize,
            A.is_agreement,
            if(C.is_checkin='1',C.is_checkin,A.is_reservation) is_reservation,
            A.cancel_remark,
            A.platform,
            A.platform_code,
            A.signer_id as renterId,
            sc.price as price,
            B.id AS contractSourceId,
            B.source_id,
            C.source_name,
            C.project_id,
            C.source_type,
            C.is_checkIn,
            D.url themeUrl,
            E.url as url,
            A.create_date,
            p.id as projectId,
            p.name as projectName,
            RE.id as releaseId
        FROM
            t_cont_contract A
            LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
            LEFT JOIN v_res_source C ON C.id in (B.source_id)
            LEFT JOIN t_res_source_configure sc ON sc.source_id=c.id
            LEFT JOIN t_res_project p ON p.id = c.project_id
            LEFT JOIN v_sys_renter r ON r.id= A.signer_id
            LEFT JOIN ( SELECT * FROM t_biz_release  where type='0' or type='3') RE ON RE.contract_id=A.id
            LEFT JOIN ( SELECT * FROM t_sys_file GROUP BY from_id ) D ON C.theme_id = D.from_id
            LEFT JOIN ( SELECT * FROM t_sys_file WHERE type='15' GROUP BY from_id ) E ON C.house_type_id = E.from_id
            LEFT JOIN t_cont_contract_info F on A.id=F.contract_id
        WHERE
            A.contract_type != '6'
            and if(A.is_organize = '0' and A.state IS NOT NULL,1,if(A.is_organize = '1' and A.state IN ( '6', '7', '8' ),1,0)) = 1
            AND A.signer_id = #{renterId} and (A.is_short is null or A.is_short ='0')
            <if test="map.keyCode != null and map.keyCode != ''">
                AND (
                C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
                or F.name LIKE CONCAT('%', #{map.keyCode}, '%')
                or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
                )
            </if>
            GROUP BY A.id
            ORDER BY A.create_date desc
    </select>

    <!-- 查询意向分页-XX公寓 -->
    <select id="selectPageByIntention" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT A.id,E.url,C.source_name,sc.price AS price,f.url themeUrl,
               '1' is_agreement, A.state,A.delivery_affirm_state as deliveryAffirmState,
               o.id yxOrderId,A.contract_type
        FROM t_bil_order o
        LEFT JOIN t_sys_file f ON f.`from_id`=o.ccb_bill_id AND f.`type`='49'
        LEFT JOIN v_res_source C ON C.id in (o.source_id)
        LEFT JOIN t_res_source_configure sc ON sc.source_id=c.id
        LEFT JOIN (SELECT * FROM t_sys_file WHERE TYPE='15' GROUP BY from_id) E ON C.house_type_id = E.from_id
        LEFT JOIN t_cont_contract A on o.ccb_bill_id=A.id and A.contract_type='6'
        LEFT JOIN t_cont_contract_info D on A.id=D.contract_id
        WHERE 1=1 AND o.`payable_payment`>0 and o.order_type='10' and A.contract_type='6'
        AND o.`payer_id`= #{renterId}
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        GROUP BY o.id
        ORDER BY o.create_date DESC
    </select>

    <!-- 查询合同分页-XX公寓 -->
    <select id="selectPageByEmp" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
            A.id,
            A.state,
            A.contract_type,
            A.start_date,
            A.end_date,
            A.is_organize,
            A.is_agreement,
            if(C.is_checkin='1',C.is_checkin,A.is_reservation) is_reservation,
            A.cancel_remark,
            A.platform,
            A.platform_code,
            A.signer_id as renterId,
            sc.price as price,
            B.id AS contractSourceId,
            B.source_id,
            C.source_name,
            C.project_id,
            C.source_type,
            C.is_checkIn,
            D.url themeUrl,
            E.url as url,
            A.create_date,
            p.id as projectId,
            p.name as projectName,
            RE.id as releaseId
        FROM
            t_cont_contract A
            LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
            LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
            LEFT JOIN t_res_source_configure sc ON sc.source_id=c.id
            LEFT JOIN t_res_project p ON p.id = c.project_id
            LEFT JOIN v_sys_renter r ON r.id= A.signer_id
            LEFT JOIN t_bil_order o ON o.contract_id=A.id
            LEFT JOIN ( SELECT * FROM t_biz_release  where type='0') RE ON RE.contract_id=A.id
            LEFT JOIN ( SELECT * FROM t_sys_file GROUP BY from_id ) D ON C.theme_id = D.from_id
            LEFT JOIN ( SELECT * FROM t_sys_file WHERE type='15' GROUP BY from_id ) E ON C.house_type_id = E.from_id
        WHERE
            if(A.is_organize = '0' and A.state IS NOT NULL,1,if(A.is_organize = '1' and A.state IN ( '6', '7', '8' ),1,0)) = 1
            AND o.payer_id = #{renterId} AND A.is_short='0'
            <if test="contractState != null and contractState != ''">
                AND A.state != #{contractState}
            </if>
            GROUP BY A.id
            ORDER BY A.start_date desc
    </select>

    <select id="selectPageBySign" resultType="java.util.Map">
        SELECT
        c.id,
        i.name,
        u.tel,
        c.contract_code,
        c.state,
        (case WHEN c.state = 9 THEN DATE_FORMAT(c.create_date,'%Y-%m-%d') else DATE_FORMAT(c.sign_date,'%Y-%m-%d') END) as apply_time,
        c.contract_type,
        s.source_name as source_name,
        s.project_id,s.id as source_id,
        r.id as isRelet,
        ifnull(w.audit_result,'N') audit_result
        FROM t_cont_contract c
        LEFT JOIN t_cont_contract_source_rel cs ON c.id = cs.contract_id
        LEFT JOIN v_res_source s ON FIND_IN_SET(s.id,cs.source_id) > 0
        LEFT JOIN v_sys_renter u ON c.signer_id=u.id
        LEFT JOIN t_base_car car ON car.contract_source_id = cs.id
        LEFT JOIN  t_biz_release r on  r.new_contract_id = c.id and r.type ='2' and r.state != 9
        LEFT JOIN t_cont_contract_info i ON i.contract_id = c.id
        LEFT JOIN t_bpm_workflow w on w.guid = c.gu_id
        WHERE 1=1 AND c.is_organize='0' and c.contract_type != '6'
        #project_datascope#
        <if test="map.state != null and map.state != ''">
            AND c.state = #{map.state}
        </if>
        <if test="map.project_id != null and map.project_id != ''">
            AND s.project_id = #{map.project_id}
        </if>
        <if test="map.states != null and map.states.size() > 0">
            AND c.state in
            <foreach collection="map.states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND s.partition_id = #{map.partitionId}
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND c.is_organize = #{map.isOrganize}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND c.contract_type = #{map.contractType}
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(d.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(d.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(d.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(d.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            s.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or u.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or c.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        <if test="map.contractIds != null and map.contractIds.size() > 0">
            AND c.id in
            <foreach collection="map.contractIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY c.id
        ORDER BY c.create_date DESC
    </select>

    <!-- 查询合同分页-企业端 -->
    <select id="selectPageByPass" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT <include refid="base" />
        FROM t_cont_contract
        WHERE is_organize = '1'
        AND signer_id = #{sysUserId}
        and not exists (select 1 from t_cont_frame_contract t where t.id = frame_contract_id)
        <if test="item.contractCode != null and item.contractCode != ''">
            AND contract_code LIKE CONCAT('%', #{item.contractCode}, '%')
        </if>
        <if test="item.state != null and item.state != ''">
            AND state = #{item.state}
        </if>
        <if test="item.isFrame=='1'.toString()">
            and frame_contract_id is not null
        </if>
        <if test="item.isFrame=='0'.toString()">
            and frame_contract_id is null
        </if>
        <if test="item.isShort=='1'.toString()">
            and is_short ='1'
        </if>
        <if test="item.isShort=='0'.toString()">
            and (is_short ='0' or is_short is null)
        </if>
        order by sign_date desc
    </select>
    <!--获取房源当前最后合同-->
    <select id="getContractBySourceId" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select * from t_cont_contract c where
        c.state not in ('3','7')
        and date_format(c.start_date,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
        and exists (select 1 from t_cont_contract_source_rel rel
                where rel.contract_id = c.id and rel.source_id = #{item.sourceId}) limit 1
    </select>

    <!--获取房源当前最后合同-->
    <select id="getBySignerId" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        select c.id value ,c.contract_code name ,c.* from t_cont_contract c where
        c.state not in ('3','7')
        and c.signer_id = #{map.signerId}
    </select>

    <!--获取房源当前生效合同-->
    <select id="getValidContractBySourceId" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select c.* from t_cont_contract_source_rel rel
        LEFT JOIN t_cont_contract c on rel.contract_id = c.id
        where rel.source_id = #{item.sourceId}
        -- and date_format(c.start_date,'%Y-%m-%d') &lt;= date_format(now(),'%Y-%m-%d')
        and c.state in ('6','8','5')
        order by c.start_date desc
        limit 0,1
    </select>
    <!--获取房源当前未起租合同-->
    <select id="getUnValidContractBySourceId" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select c.* from t_cont_contract_source_rel rel
        LEFT JOIN t_cont_contract c on rel.contract_id = c.id
        where rel.source_id = #{item.sourceId}
        and c.state in ('5')
    </select>
    <!--获取用户当前生效合同的服务电话-->
    <select id="getServicePhoneByRenterId" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        select DISTINCT p.name as projectName,i.service_phone servicePhone
        from t_cont_check_in_user u
        left join t_cont_contract_source_rel r on u.contract_source_id = r.id
        left join t_res_source s ON FIND_IN_SET(s.id,r.source_id) > 0
        left join t_res_project p on s.project_id = p.id
        left join t_res_project_info i on i.project_id = p.id
        where EXISTS (select 1 from t_cont_contract c where c.state in('6','8'))
        and p.id is not null
        <if test="renterId != null and renterId != ''">
            and u.renter_id = #{renterId}
        </if>
        group by u.id
    </select>
    <!--u管人才房查看信息-->
    <select id="utContractInfo" resultType="java.util.Map">
       SELECT date_format(c.start_date,'%Y-%m-%d')start_date,date_format(c.end_date,'%Y-%m-%d') end_date,c.contract_code
        ,u.id_type,u.id_no,u.`name`,u.tel,l.price,c.pay_type
        from t_cont_contract c
        LEFT JOIN v_sys_renter u on u.id=c.signer_id
		LEFT JOIN t_cont_contract_source_rel r on r.contract_id=c.id
        LEFT JOIN t_cont_rent_ladder l on l.contract_source_id= r.id
        where c.id=#{contractId} and r.source_id=#{sourceId}
          group by c.id
    </select>

    <select id="getContractListBySourceId" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        c.id,
        c.is_organize,
        c.contract_type,
        c.contract_code,
        c.start_date,
        c.end_date,
        c.state,
        c.platform,
        c.platform_code,
        cs.id contract_source_id,
        ci.NAME,
        cl.price,
        ci.subsidy_sum,
        co.checkout_date
        FROM
        t_cont_contract c,
        (SELECT * FROM (select * from t_cont_rent_ladder order by
        <if test="searchDate != null">
            if(#{searchDate} BETWEEN start_date and end_date,0,1) asc,
        </if>
        start_date desc ) t GROUP BY contract_source_id) cl,
        t_cont_contract_source_rel cs,
        t_cont_contract_info ci
        LEFT JOIN t_biz_release co ON co.contract_id = ci.contract_id
        WHERE
        c.id = cs.contract_id
        AND ci.contract_id = c.id
        AND cl.contract_source_id = cs.id
        AND cs.source_id = #{sourceId}
        ORDER BY
        c.start_date DESC

    </select>

    <!--条件获取所有合同-->
    <select id="queryList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        c.id,
        c.platform,
        c.platform_code,
        v.id as renterId,
        (CASE when v.name is null then v.username else v.name end) as name,
        ci.name as renterName,
        p.name as projectName,
        p.id as projectId,
        concat(p.name,'-',par.name,'-',s.code) as sourceName,
        s.id as sourceId,
        cs.id as contractSourceId,
        DATE_FORMAT(c.start_date, '%Y-%m-%d') as startDate,
        DATE_FORMAT(c.end_date, '%Y-%m-%d') as endDate,
        c.is_organize,c.contract_code contractCode,c.create_date createDate,c.state,r.id as releaseId,r.state as releaseState
        FROM
        t_cont_contract c
        LEFT JOIN t_cont_contract_source_rel cs ON c.id = cs.contract_id
        LEFT JOIN t_res_source s ON FIND_IN_SET(s.id,cs.source_id) > 0
        LEFT JOIN t_res_plan_partition par ON par.id = s.partition_id
        LEFT JOIN t_res_project p on p.id = s.project_id
        LEFT JOIN v_sys_renter v ON v.id = c.signer_id
        LEFT JOIN t_biz_release r on r.contract_id = c.id and r.state !=9
        INNER JOIN t_cont_contract_info ci on c.id = ci.contract_id
        where 1=1
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and c.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            and c.is_organize = #{map.isOrganize}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            and c.contract_type = #{map.contractType}
        </if>
        <if test="map.states != null and map.states.size() > 0" >
            AND c.state in
            <foreach collection="map.states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.statesStr != null and map.statesStr !=''">
            AND  c.state in
            <foreach collection="map.statesStr.split(',')" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(c.end_date, '%Y-%m-%d') = DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.state != null and map.state != ''">
            AND A.state = #{map.state}
        </if>
        group by c.id
        order by c.end_date asc
    </select>


    <select id="selectTalentContract" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT
        C.id,
        C.contract_code,
        C.contract_type,
        C.platform
        FROM t_cont_contract C
        LEFT JOIN t_cont_contract_source_rel D ON C.id = D.contract_id
        LEFT JOIN t_res_source E ON FIND_IN_SET(E.id,D.source_id) > 0
        LEFT JOIN t_cont_temp T ON T.id = C.contract_templet_id
        WHERE E.is_talent = 1 and C.contract_type= 1 /*and T.is_talent = 1*/
        AND C.signer_id = #{renter_id} and C.state in ('6','8')
        GROUP BY C.id
    </select>

    <update id="syncContractType">
        update t_cont_contract c
        inner join t_cont_contract_source_rel cs on cs.contract_id = c.id
        inner join t_res_source s on cs.source_id = s.id
        set contract_type = '2'
        where s.source_type = '1'
    </update>


    <select id="getRentContractList" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select * from t_cont_contract c
        where 1 = 1
        and  exists (
            select 1 from t_cont_contract_source_rel cs,t_res_source s
            where FIND_IN_SET(s.id,cs.source_id) > 0
            and exists (select 1 from t_res_project p  where p.id = s.project_id and p.operate_state='0')
            and s.project_id != 'c408e0d4175c4c5ab58e7ba66160e0f2'
        )
        and c.state in ('6')
        and c.platform  = '0'
    </select>

    <select id="selectByContractId" resultMap="signVo">
        select c.*,ct.*,ci.* from t_cont_contract c  left join t_cont_temp ct on c.contract_templet_id = ct.id
        LEFT JOIN t_cont_contract_info ci on c.id = ci.contract_id
        where c.id = #{contractId}
    </select>

    <select id="getLiveContract" resultMap="BaseCSResultMap">
        select <include refid="Base_Column_CS_SQL"/>
        from t_cont_contract c,t_cont_contract_source_rel cs,t_res_source s
        where c.id = cs.contract_id and cs.source_id = s.id
        and (c.state = '6' or (c.state = '8' and cs.settle_state = '1'))
        and (c.frame_contract_id is null or (c.frame_contract_id is not null and c.life_pay_payer = '2'))
        <if test="startDate != null">
            and c.start_date &lt; #{startDate}
            and c.end_date &gt;= #{startDate}
        </if>
        <if test="projectId != null">
            and s.project_id = #{projectId}
        </if>

        <if test="projectCode!=null and projectCode != ''">
            and EXISTS (select 1 from t_res_project_para tp where tp.param_code = #{projectCode} and param_value ='1'
            and tp.project_id = s.project_id)
        </if>
        and EXISTS (select 1 from t_res_project p where s.project_id = p.id AND p.operate_state = '0')
        and c.platform ='0'
        order by s.project_id
    </select>

    <resultMap type="cn.uone.bean.entity.business.cont.vo.SignVo" id="signVo">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="contract_templet_id" property="contractTempletId"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_code" property="contractCode"/>
        <result column="signer_id" property="signerId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="sign_type" property="signType"/>
        <result column="sign_date" property="signDate"/>
        <result column="pay_type" property="payType"/>
        <result column="cost_configure_id" property="costConfigureId"/>
        <result column="rent_pay_payer" property="rentPayPayer"/>
        <result column="life_pay_payer" property="lifePayPayer"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="paper_code" property="paperCode"/>
        <result column="state" property="state"/>
        <result column="is_organize" property="isOrganize"/>
        <result column="is_agreement" property="isAgreement"/>
        <result column="is_pet" property="isPet"/>
        <result column="is_reservation" property="isReservation"/>
        <result column="cancel_remark" property="cancelRemark"/>
        <!-- 这是映射 -->
        <association property="contContractInfoEntity" javaType="cn.uone.bean.entity.business.cont.ContContractInfoEntity">
            <id property="contractId" column="id" />
            <result column="id" property="id"/>
            <result column="create_by" property="createBy"/>
            <result column="create_date" property="createDate"/>
            <result column="update_by" property="updateBy"/>
            <result column="update_date" property="updateDate"/>
            <result column="contract_id" property="contractId"/>
            <result column="name" property="name"/>
            <result column="tel" property="tel"/>
            <result column="id_type" property="idType"/>
            <result column="id_no" property="idNo"/>
            <result column="is_subsidy" property="isSubsidy"/>
            <result column="subsidy_sum" property="subsidySum"/>
            <result column="special_item" property="specialItem"/>
            <result column="special_agreement" property="specialAgreement"/>
            <result column="purpose" property="purpose"/>
            <result column="business_scope" property="businessScope"/>
            <result column="business_brand" property="businessBrand"/>
            <result column="urgenter" property="urgenter"/>
            <result column="urgent_tel" property="urgentTel"/>
            <result column="address" property="address"/>
        </association>

        <association property="contractTempletEntity" javaType="cn.uone.bean.entity.business.cont.ContContractTempletEntity">
            <id property="id" column="contractTempletId" />
            <result column="id" property="id"/>
            <result column="type" property="type"/>
            <result column="create_by" property="createBy"/>
            <result column="create_date" property="createDate"/>
            <result column="update_by" property="updateBy"/>
            <result column="update_date" property="updateDate"/>
            <result column="name" property="name"/>
            <result column="contract_templet_type" property="contractTempletType"/>
            <result column="customer_type" property="customerType"/>
            <result column="project_id" property="projectId"/>
            <result column="is_talent" property="isTalent"/>
            <result column="content" property="content"/>
            <result column="subsidy_price" property="subsidyprice"/>
            <result column="subsidy_person" property="subsidyPerson"/>
            <result column="costId" property="costId"/>
        </association>

        <collection property="contractSourceRelBoList" javaType="java.util.ArrayList" ofType="cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo" select="cn.uone.business.cont.dao.ContContractSourceRelDao.selectByContractId" column="id">
        </collection>

        <collection property="costConfigureVoList" javaType="ArrayList" ofType="cn.uone.bean.entity.business.res.vo.ResCostConfigureVo" select="cn.uone.business.res.dao.ResCostConfigureDao.queryByCostConfigureId" column="cost_configure_id">
        </collection>

    </resultMap>

    <select id="getMaxPaperCode" resultType="java.lang.String">
      SELECT max(substring(c.paper_code, -3)) FROM t_cont_contract c
        where 1=1
        and c.paper_code like CONCAT(#{preCode},'%') and CHAR_LENGTH(c.paper_code) = 14
    </select>

    <select id="renterContractList" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT c.id,c.contract_type,c.is_organize,s.id source_id,c.paper_code,
        s.source_name,CONCAT(date_format(c.start_date,'%Y-%m-%d'),' 到 ',date_format(c.end_date,'%Y-%m-%d'))sign_time
        ,ifnull(yqo.yuqi,0) yuqi,ifnull(qjo.qianjiao,0) qianjiao
        FROM t_cont_contract c
        LEFT JOIN t_cont_contract_source_rel r on r.contract_id = c.id
        LEFT JOIN v_res_source s on FIND_IN_SET(s.id,r.source_id) > 0
        left join
        (
        select count(1) yuqi,o.contract_id,o.source_id from t_bil_order o where o.order_type !='21' and (o.is_push ='1'
        and o.pay_state in('10','30') and DATE_FORMAT(now(),'%Y-%m-%d') >DATE_FORMAT(o.payable_time,'%Y-%m-%d') and
        o.is_discard='0') or (DATE_FORMAT(o.pay_time,'%Y-%m-%d')>DATE_FORMAT(o.payable_time,'%Y-%m-%d') and
        o.pay_state='20' and o.is_discard='0') GROUP BY o.contract_id, o.source_id
        ) yqo  on yqo.contract_id = c.id and yqo.source_id =r.source_id
        left join
        (
        select count(1) qianjiao,o.contract_id,o.source_id from t_bil_order o where o.is_push ='1' and o.pay_state in
        ('10','30') and o.is_discard ='0' and o.order_type !='21' GROUP BY o.contract_id, o.source_id
        ) qjo  on qjo.contract_id = c.id and qjo.source_id =r.source_id
        WHERE   c.signer_id = #{map.signerId}
        <if test="map.isOrganize!=null and map.isOrganize != ''">
            and c.is_organize = #{map.isOrganize}
        </if>
        <if test="map.state!=null and map.state != ''">
            <if test="map.state == '6'.toString">
                and c.state in ('6','8')
            </if>
            <if test="map.state == '7'.toString">
                and c.state = #{map.state}
            </if>
        </if>
        GROUP BY c.id
    </select>

    <select id="getExRemark" resultType="java.util.Map">
        SELECT IFNULL(CONCAT(r.`name`,'-',s.source_name),r.name) remark FROM v_sys_renter r
        LEFT JOIN t_cont_contract c ON c.signer_id = r.id AND c.state in ('5','6','8')
        LEFT JOIN t_cont_contract_source_rel sr on sr.contract_id = c.id
        LEFT JOIN v_res_source s on FIND_IN_SET(s.id,sr.source_id) > 0
        WHERE   r.id = #{renterId}
        GROUP BY r.id
        ORDER BY c.contract_type  LIMIT 1
  </select>


    <select id="getExContractTab" resultType="java.util.Map">
      SELECT CASE WHEN a.state = '2' THEN   '已退租' ELSE '已签约' END tabs
        FROM (
        SELECT CASE
        WHEN c.state='7' THEN 2
        WHEN c.state in ('5','6','8') THEN 1  END state FROM t_cont_contract c WHERE
        c.signer_id = #{renterId}
        )a ORDER BY a.state ASC  LIMIT 1
  </select>

    <select id="getExOrdertTab" resultType="java.util.Map">
       SELECT IF(qjo.qianjiao IS NULL ,'无欠缴账单','有欠缴账单') tabs from v_sys_renter r
        left join (
            select count(1) qianjiao,o.payer_id from t_bil_order o where o.is_push ='1' and o.pay_state in ('10','30') and o.is_discard ='0' and o.order_type!='21' group by o.payer_id
        ) qjo on qjo.payer_id = r.id
          where  r.id =  #{renterId}
  </select>


    <select id="getExRepairTab" resultType="java.util.Map">
       SELECT IF(COUNT(*)=0,'无报修投诉过','有报修投诉过')tabs FROM t_res_repair WHERE renter_id =   #{renterId}
  </select>

    <select id="getOldContractByNewContractId" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select c1.* from t_biz_release r inner join t_cont_contract c on r.new_contract_id = c.id
        inner join  t_cont_contract c1 on r.contract_id = c1.id
        where c.id = #{newContractId}
    </select>

    <select id="queryOldContract" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT t.* FROM t_cont_contract  t WHERE t.`state` in('6','14') AND t.`end_date` &lt;  CURDATE()
    </select>

    <select id="getCheckout1Day" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT DISTINCT c.* FROM t_cont_contract c
        LEFT JOIN t_biz_release r ON r.contract_id = c.id
        WHERE 1 = 1 AND r.type = 2 AND C.state='7' AND c.is_organize = 0
        AND DATEDIFF(NOW(), c.end_date) &gt; 0 AND DATEDIFF(NOW(), c.end_date) &lt; 15
        and not EXISTS(
        select 1 from t_bil_order o
        LEFT JOIN t_bil_order_item oi ON oi.order_id = o.id
        WHERE 1=1 and o.contract_id = c.id  AND o.is_initial = '1'
        AND ((o.order_type in ('30') AND c.end_date=oi.end_time) or o.order_type in ('130','140','211','210'))
        )
    </select>

    <resultMap id="selectVoMap" type="cn.uone.bean.entity.business.res.vo.SelectVo">
        <result column="value" property="value"/>
        <result column="name" property="name"/>
    </resultMap>

<!--    <select id="getSourceIdList" resultMap="selectVoMap" >-->
<!--        select t.source_id value ,v.source_name name  from t_cont_contract_source_rel t , v_res_source v,t_cont_check_in_user ch-->
<!--        where t.source_id = v.id and ch.contract_source_id=t.id and ch.examine='1'-->
<!--        <if test="contractId != null and contractId != ''">-->
<!--            and EXISTS (select 1 from t_cont_contract ts where ts.id = t.contract_id and ts.signer_id = #{contractId}-->
<!--            and ts.state in ('6','8'))-->
<!--        </if>-->
<!--    </select>-->

    <!--2025.3.21 删除入住状态判断： and ch.examine = '1' -->
    <select id="getSourceIdList" resultMap="selectVoMap" >
        select v.id value ,v.source_name name
        from ( select t.source_id ,ch.renter_id , tc.state,t.id from t_cont_contract_source_rel t ,
                t_cont_check_in_house ch ,t_cont_contract tc where tc.id = t.contract_id
                  and ch.contract_source_id = t.id  ) r
        INNER JOIN v_res_source v on FIND_IN_SET(v.id,r.source_id) > 0
        <if test="renterId != null and renterId != ''">
            and r.renter_id=#{renterId}
            and r.state in ('6','8')
        </if>

    </select>


    <select id="getOrgFixContract" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT DISTINCT c.* FROM t_cont_contract c
        LEFT JOIN t_biz_release r ON r.contract_id = c.id
        WHERE 1 = 1 AND r.type = 2 AND C.state='7' AND c.is_organize = 0
        AND DATEDIFF(NOW(), c.end_date) &gt; 0 AND DATEDIFF(NOW(), c.end_date) &lt; 15
        and not EXISTS(
        select 1 from t_bil_order o
        LEFT JOIN t_bil_order_item oi ON oi.order_id = o.id
        WHERE 1=1 and o.contract_id = c.id  AND o.is_initial = '1'
        AND ((o.order_type in ('30') AND c.end_date=oi.end_time) or o.order_type in ('130','140','211','210'))
        )
    </select>

    <select id="getHydropowerStartTime" resultType="java.lang.String">
         select
           case when zdrq is null then LEFT(rzrq, 10) else left(DATE_ADD(zdrq, INTERVAL 1 DAY), 10) end as sdate
         from (
            select
             e.start_date as rzrq
              ,
              (select d.end_time from  t_bil_order c,t_bil_order_item d where c.id =d.order_id and d.payment>0 and d.order_item_type = #{order_item_type}
               and c.pay_state in ('10','20') and c.contract_id=e.id and c.is_discard = '0' order by d.end_time desc LIMIT 1) as zdrq
            from t_cont_contract e where e.id = #{contract_id}
          ) f
    </select>

    <select id="getExpirings" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT <include refid="Base_Column_List" />,C.is_checkin as isCheckIn,ch.renter_id as renterId,ch.examine as checkInState,
        ch.id as checkInHouseId
        FROM t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=B.id AND ch.examine!=2
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
        LEFT JOIN t_cont_contract_info D ON A.id = D.contract_id
        LEFT JOIN t_biz_release R ON R.contract_id = A.id AND R.state!= '9'
        LEFT JOIN t_base_car CAR ON CAR.contract_source_id = B.id
        LEFT JOIN v_sys_user u on u.id = A.manager
        WHERE 1 = 1 #project_datascope#
        AND C.project_id = #{map.projectId}
        AND A.state="6"
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and A.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND C.partition_id = #{map.partitionId}
        </if>
        <if test="map.antistop != null and map.antistop != ''">
            AND (A.contract_code LIKE CONCAT('%', #{map.antistop}, '%') OR C.code LIKE CONCAT('%', #{map.antistop}, '%')
            OR D.name LIKE CONCAT('%', #{map.antistop}, '%') OR CAR.num LIKE CONCAT('%', #{map.antistop}, '%'))
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND A.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND A.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (A.is_short = '0' or A.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND A.is_short = '1'
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND A.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND A.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (A.platform = '0' or A.platform is null or A.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND A.platform = '1'
        </if>
        <if test="map.isSubsidy != null and map.isSubsidy != ''">
            AND D.is_subsidy = #{map.isSubsidy}
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(A.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(A.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(A.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            C.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or D.name LIKE CONCAT('%', #{map.keyCode}, '%')
            or A.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        GROUP BY A.id
    </select>

    <select id="exportSourceContract" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        select c.contract_code,s.source_name sourceName, s.area roomArea, rsc.low_price lowPrice,rsc.price originalPrice,
        c.cash_pledge rentalPrice,tsd.discount
        ,v.name , c.is_organize ,u.nick_name managerName,DATE_FORMAT(c.start_date,'%Y-%m-%d') start_date ,DATE_FORMAT(c.end_date,'%Y-%m-%d') end_date
        ,c.state , c.contract_type ,c.create_type , r.state releaseState ,DATE_FORMAT(R.checkout_date,'%Y-%m-%d') checkoutDateStr
        from v_res_source s
            left join (select cc.*,ccs.source_id,ccs.cash_pledge from t_cont_contract cc , t_cont_contract_source_rel ccs
            where ccs.contract_id = cc.id and cc.state in ('5','6')) c on FIND_IN_SET(s.id,c.source_id) > 0
            left join t_res_source_configure rsc on rsc.source_id = s.id
            left join (select * from t_sale_demand tsm where tsm.is_fix ='1' group by tsm.user_id) tsd on tsd.source_id = c.source_id
            left join v_sys_renter v on v.id = c.signer_id
            left join v_sys_user u on u.id = c.manager
            left join t_biz_release R ON R.contract_id = c.id AND R.state!= '9'
        WHERE 1 = 1
        AND s.project_id = #{map.projectId}
        <if test="map.frameContractId != null and map.frameContractId != ''">
            and c.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND s.partition_id = #{map.partitionId}
        </if>
        <if test="map.platformCode != null and map.platformCode != ''">
            AND c.platform_code LIKE CONCAT('%', #{map.platformCode}, '%')
        </if>
        <if test="map.isOrganize != null and map.isOrganize != ''">
            AND c.is_organize = #{map.isOrganize}
        </if>
        <if test="map.isShort != null and map.isShort == '0'.toString">
            AND (c.is_short = '0' or c.is_short is null)
        </if>
        <if test="map.isShort != null and map.isShort == '1'.toString">
            AND c.is_short = '1'
        </if>
        <if test="map.frameId != null and map.frameId != ''">
            AND c.frame_contract_id = #{map.frameId}
        </if>
        <if test="map.contractType != null and map.contractType != ''">
            AND c.contract_type = #{map.contractType}
        </if>
        <if test="map.platform != null and map.platform == 0">
            AND (c.platform = '0' or c.platform is null or c.platform ='')
        </if>
        <if test="map.platform != null and map.platform == 1">
            AND c.platform = '1'
        </if>
        <if test="map.endDate != null">
            AND DATE_FORMAT(c.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDate}, '%Y-%m-%d')
        </if>
        <if test="map.startDate != null">
            AND DATE_FORMAT(c.start_date, '%Y-%m-%d') >= DATE_FORMAT(#{map.startDate}, '%Y-%m-%d')
        </if>
        <if test="map.signDateStart != null">
            AND DATE_FORMAT(c.sign_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.signDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.signDateEnd != null">
            AND DATE_FORMAT(c.sign_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.signDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.endDateStart != null">
            AND DATE_FORMAT(c.end_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.endDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.endDateEnd != null">
            AND DATE_FORMAT(c.end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.endDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.startDateStart != null">
            AND DATE_FORMAT(c.start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.startDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.startDateEnd != null">
            AND DATE_FORMAT(c.start_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.startDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateStart != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDateStart}, '%Y-%m-%d')
        </if>
        <if test="map.checkoutDateEnd != null">
            AND DATE_FORMAT(R.checkout_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDateEnd}, '%Y-%m-%d')
        </if>
        <if test="map.keyCode != null and map.keyCode != ''">
            AND (
            s.source_name LIKE CONCAT('%', #{map.keyCode}, '%')
            or c.contract_code LIKE CONCAT('%', #{map.keyCode}, '%')
            )
        </if>
        group by s.id
        order by  s.partition_id , s.code
    </select>

    <select id="getEffectiveContract" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        select t.* from t_cont_contract t , t_cont_contract_source_rel ct , t_cont_check_in_user tu
        where t.id = ct.contract_id and ct.id = tu.contract_source_id and t.state in ('5','6','8','12','13','14')
        <if test="renterId != null and renterId != ''">
            AND tu.renter_id = #{renterId}
        </if>
        limit 1
    </select>

    <select id="getRenterList" resultType="cn.uone.bean.entity.business.sys.vo.RenterVo">
           select v.tel , v.id_no , v.openid , v.name ,v.id
        from t_cont_contract c , t_cont_contract_source_rel ct , v_res_source s , v_sys_renter v
        where c.id = ct.contract_id and FIND_IN_SET(s.id,ct.source_id) > 0 and v.id = c.signer_id and c.state in ('5','6')
        <if test="projectId != null and projectId != ''">
            AND s.project_id = #{projectId}
        </if>
        <if test="partitionId != null and partitionId != ''">
            AND s.partition_id = #{partitionId}
        </if>
        <if test="sourceId != null and sourceId != ''">
            AND s.id = #{sourceId}
        </if>
        <if test="floor != null and floor != ''">
            AND s.floor = #{floor}
        </if>
        group by c.id
    </select>

    <select id="getByUnsigned" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        A.*,B.source_id as sourceId,u.real_name as managerName,r.name,r.tel,r.id_no as id_no,C.source_name
        FROM
        t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
        LEFT JOIN t_res_project p ON p.id = c.project_id
        LEFT JOIN v_sys_renter r ON r.id= A.signer_id
        LEFT JOIN v_sys_user u on u.id = A.manager
        where
        A.state = '9'
        AND C.project_id = #{vo.projectId}
        <if test="vo.name != null and vo.name != ''">
            AND r.name LIKE CONCAT('%', #{vo.name}, '%')
        </if>
        <if test="vo.tel != null and vo.tel != ''">
            AND r.tel LIKE CONCAT('%', #{vo.tel}, '%')
        </if>
        <if test="vo.sourceName != null and vo.sourceName != ''">
            AND C.source_name LIKE CONCAT('%', #{vo.sourceName}, '%')
        </if>
        group by A.id
        order by A.create_date desc
    </select>

    <select id="getLastPaperCode" resultType="java.lang.String">
        SELECT t.paper_code FROM t_cont_contract t,t_cont_contract_source_rel csr,v_res_source s
        WHERE t.id=csr.`contract_id` AND FIND_IN_SET(s.id,csr.source_id) > 0
        <if test="projectId != null and projectId != '' ">
            and s.project_id = #{projectId}
        </if>
        and t.contract_type != '6' and t.paper_code not in('厦地铁资产（合）【岩内公寓】20253538号','厦地铁资产（合）【岩内公寓】20253539号')
        ORDER BY t.`paper_code` DESC limit 1
    </select>
    <select id="getLastYxPaperCode" resultType="java.lang.String">
        SELECT t.paper_code FROM t_cont_contract t
        WHERE 1=1
        and t.contract_type = '6'
        ORDER BY t.`create_date` DESC limit 1
    </select>

    <select id="getToAuditContractList" resultMap="BaseResultMap">
        SELECT t.* FROM t_cont_contract t,t_cont_contract_source_rel cs,t_res_source s
        WHERE 1=1 AND t.id=cs.`contract_id` AND s.id IN (cs.`source_id`)
        <if test="map.projectId != null and map.projectId != ''">
            and s.project_id = #{map.projectId}
        </if>
        <if test="map.contractIds != null and map.contractIds.size() > 0" >
            AND t.id in
            <foreach collection="map.contractIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t.id
    </select>

    <select id="repetitionByPaperCode" resultType="java.lang.Integer">
        select count(id) from t_cont_contract
        where paper_code = #{paperCode}
    </select>

    <select id="selectPageByIntent" resultType="java.util.Map">
        SELECT c.id,s.`source_name`,r.name,r.tel,c.`paper_code`,c.pay_type,
               date_format(c.start_date,'%Y-%m-%d') start_date,
                date_format(c.end_date,'%Y-%m-%d') end_date,c.state,
               sd.`price`,sd.`deposit`,sd.`cashPledge`,o.`pay_state`,f.name fileName,f.url,
               (case WHEN c.state = '6' THEN DATE_FORMAT(c.sign_date,'%Y-%m-%d') else DATE_FORMAT(c.delivery_time,'%Y-%m-%d') END) as apply_time
        FROM t_cont_contract c,t_bil_order o,v_sys_renter r,v_res_source s,t_sale_demand sd,t_sys_file f
        WHERE 1=1 AND c.contract_type='6' AND c.id=o.`contract_id` AND o.`order_type`='10' AND sd.`user_id`=o.`payer_id` AND sd.`source_id`=o.`source_id`
          AND o.`source_id`=s.id AND c.`signer_id`=r.`id` AND f.`from_id`=c.id AND f.`type`='49'
        #project_datascope#
        <if test="map.state != null and map.state != ''">
            AND c.state = #{map.state}
        </if>
        <if test="map.project_id != null and map.project_id != ''">
            AND s.project_id = #{map.project_id}
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            AND (s.source_name like CONCAT('%', #{map.keyWord}, '%')
                or r.name like CONCAT('%', #{map.keyWord}, '%'))
        </if>
        <if test="map.contractIds != null and map.contractIds.size() > 0">
            AND c.id in
            <foreach collection="map.contractIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY c.`update_date` DESC
    </select>

    <select id="getIntentBySourceIdAndRenterId" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.`start_date`,'%Y-%m-%d') shareStartTime,DATE_FORMAT(t.`end_date`,'%Y-%m-%d') shareEndTime,t.`contract_templet_id` templateId,
               t.`cost_configure_id` costId,t.`pay_type` payType,o.id yxOrderid,
               o.`payment` due,o.`pay_state` payState,p.name tempName
        FROM t_bil_order o,t_cont_contract t
           LEFT JOIN t_cont_temp p ON p.id=t.`contract_templet_id`
        WHERE 1=1 AND t.contract_type='6' AND t.`state` != '3'
        AND t.id=o.`ccb_bill_id` AND o.`order_type`='10' AND o.`pay_state` !='40' and o.intention not in ('3','4')
        AND o.`source_id`=#{sourceId} AND o.`payer_id`=#{renterId}
    </select>

    <select id="getContByRenter" resultType="java.util.Map">
        select a.id as value,c.source_name as name,r.name,r.`name` as renterName
        FROM
        t_cont_contract A
        LEFT JOIN t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN v_res_source C ON FIND_IN_SET(C.id,B.source_id) > 0
        LEFT JOIN v_sys_renter r ON r.id= A.signer_id
        where A.signer_id = #{renterId}
        and A.state in ('5','6')
        and NOT EXISTS(SELECT 1 FROM t_cont_contract_file_rel t where t.contract_id=A.id and t.state in('1','6'))
    </select>

    <select id="getLiveContractByMonth" resultType="cn.uone.bean.entity.business.cont.ContContractEntity">
        SELECT t.* FROM t_cont_contract t
            LEFT JOIN t_cont_contract_source_rel cs ON cs.`contract_id`=t.id
            LEFT JOIN t_res_source s ON FIND_IN_SET(s.id,cs.`source_id`)&gt;0
            LEFT JOIN t_res_project_para pp ON pp.`project_id`=s.`project_id` AND pp.`param_code`='XMCS_0211'
        WHERE t.state='6' AND pp.`param_value`='1'
          AND DATE_FORMAT(t.`end_date`,'%Y%m')&gt;=#{yyyyMM}
        GROUP BY t.id
    </select>

    <select id="getRenterNum" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT t.id) renterNum
        FROM v_sys_renter t,t_cont_contract c,
             t_cont_contract_source_rel sr,t_res_source s,t_res_project p,t_sys_area a
        WHERE t.id=c.`signer_id` AND c.`state` IN('5','6')
          AND c.id=sr.`contract_id` AND FIND_IN_SET(s.id,sr.`source_id`)&gt;0 AND s.`project_id`=p.`id`
          AND p.`city_id`=a.id
            <if test="cityCode!='' and cityCode!=null">
                AND a.code = #{cityCode}
            </if>
    </select>

    <select id="getMonthSignCounts" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.`sign_date`,'%Y%m') signDate,
               COUNT(DISTINCT t.id) contNum,
               SUM(o.`payable_payment`) totalPayment,
               ROUND(SUM(o.`payable_payment`)/COUNT(DISTINCT t.id),2) payment
        FROM t_cont_contract t
                 LEFT JOIN t_bil_order o ON o.`contract_id`=t.id AND o.`order_type`='20' AND o.`pay_state` !='40'
        WHERE t.`state` != '3' AND t.`sign_date` IS NOT NULL
        GROUP BY DATE_FORMAT(t.`sign_date`,'%Y%m')
        ORDER BY DATE_FORMAT(t.`sign_date`,'%Y%m')
    </select>

    <select id="countRentConts" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.`start_date`,'%Y%m') yearMonth,COUNT(1) rentNum
        FROM t_cont_contract t
        WHERE t.state NOT IN('3','9','1')
        <if test="year !=null and year !=''">
            AND DATE_FORMAT(t.`start_date`,'%Y')=#{year}
        </if>
        GROUP BY DATE_FORMAT(t.`start_date`,'%Y%m')
        ORDER BY DATE_FORMAT(t.`start_date`,'%Y%m')
    </select>

    <select id="countRentContsArea" resultType="java.math.BigDecimal">
        SELECT sum(IFNULL(s.area, 0)) area
        FROM t_cont_contract t,
             t_cont_contract_source_rel cr,
             t_res_source s
        WHERE t.id = cr.contract_id
          AND cr.source_id = s.id
          AND t.state NOT IN ('3', '9', '1')
    </select>

    <select id="selectContractsForRecord" resultType="java.util.Map">
        SELECT t.id FROM t_cont_contract t,t_cont_contract_source_rel cr
        WHERE t.id=cr.`contract_id` AND cr.record_state IN ('0','-1')
        AND t.state IN ('6','7') AND t.`sign_date` &lt; CURDATE()
        <if test="map.projectId != null and map.projectId != ''">
            AND EXISTS (SELECT 1 FROM t_res_source s
            WHERE FIND_IN_SET(s.id,cr.`source_id`)>0 AND s.`project_id`=#{map.projectId})
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            AND t.id=#{map.contractId}
        </if>
        <if test="map.isYnbh != null and map.isYnbh == '1'">
            AND NOT EXISTS (TIMESTAMPDIFF(MONTH, t.start_date, t.end_date) &lt; 6)
        </if>
        AND NOT EXISTS (SELECT 1 FROM t_res_source s
        WHERE FIND_IN_SET(s.id,cr.`source_id`)>0 AND s.`project_id`='634db6a33ac66818762160b88a065547'
        and s.id in('4698ad02f89430672f2381b1a7023d63','5d754cc2dbbcb9472a6d302431018e5e','19b33dcf18f87bcd5e6d31cac709d3e9','cf0a500f48c3446192f19ebd151e165a','c789a1dff20288bf22dcfa0ca9ee4f78','5d302d75474357b25b912a4d0e8ffefe','f790685b83ec67976b0a7e61344d0d72','de606da453e416fa0fde89a176ee878e','e801a9f5160e3db7628343b4bffbde63','f0e988ab66bd12cd7e18b8be0621a77a'))
    </select>

    <select id="expiringIn30days" resultType="cn.uone.bean.entity.business.cont.vo.ContContractVo">
        SELECT
        c.id, frame_contract_id, contract_templet_id, contract_type, contract_code,
        signer_id, start_date, end_date, sign_type, sign_date, pay_type, cost_configure_id,
        rent_pay_payer, life_pay_payer, fix_life_pay_payer, invoice_type, paper_code, c.state,
        c.create_by, c.create_date, c.update_by, c.update_date, is_organize, is_agreement,
        is_pet, is_reservation, cancel_remark, c.remark, platform, platform_code, sync_state,
        time, urge_time, c.is_short, is_gd, is_yj, manager, price, create_type, gu_id,
        free_start_date, free_end_date, process_instance_id, process_task_id,
        state_owned_assets_contract_id, first_party, rental_area, lease_use,
        electricity_deposit, plant_deposit, other_lease, rent_date, tax_point,
        c.leasehold_area, rent_free_period,
        old_renter_id, year_increase, year_num, stop_time, total_amount,
        original_rent, price_strategy_id,
        delivery_affirm_state, delivery_time, rent_type,
        p.company_id AS companyId,
        s.source_name AS sourceName,
        CASE WHEN
        DATEDIFF(DATE_FORMAT(c.end_date,'%Y-%m-%d'),CURDATE()) BETWEEN 0 AND 15 THEN 15
        ELSE 30 END AS days
        FROM t_cont_contract c,v_res_source s,t_cont_contract_source_rel cs,t_res_project p
        WHERE c.id = cs.contract_id
        AND cs.source_id = s.id
        AND p.id = s.project_id
        AND c.contract_type = '1'
        AND c.state = '6'
        AND DATEDIFF(DATE_FORMAT(c.end_date,'%Y-%m-%d'),CURDATE()) BETWEEN 0 AND 30
        AND p.code IN
        <foreach collection="projectCodes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </select>

</mapper>
