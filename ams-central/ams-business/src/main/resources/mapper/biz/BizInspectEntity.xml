<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.biz.dao.BizInspectDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.biz.BizInspectEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="renter_id" property="renterId" />
        <result column="release_id" property="releaseId" />
        <result column="contract_source_id" property="contractSourceId" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        renter_id, release_id, contract_source_id, state
    </sql>

    <select id="getLastApply" resultMap="BaseResultMap">
        select a.* from t_biz_inspect a where 1=1
        <if test="map.contractSourceId != null and map.contractSourceId != ''">
            AND a.contract_source_id = #{map.contractSourceId}
        </if>
        <if test="map.notInStates != null and map.notInStates.size() > 0">
            AND a.state  not in
            <foreach collection="map.notInStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by create_date desc limit 1
    </select>

</mapper>
