<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.report.dao.ReportReleaseOrderDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.report.ReportReleaseOrderEntity">
    <result column="id" property="id" />
    <result column="create_date" property="createDate" />
    <result column="create_by" property="createBy" />
    <result column="update_date" property="updateDate" />
    <result column="update_by" property="updateBy" />
        <result column="project_id" property="projectId" />
        <result column="partition_id" property="partitionId" />
        <result column="source_id" property="sourceId" />
        <result column="order_id" property="orderId" />
        <result column="out_or_change_time" property="outOrChangeTime" />
        <result column="yszj" property="yszj" />
        <result column="yszj_start_time" property="yszjStartTime" />
        <result column="yszj_end_time" property="yszjEndTime" />
        <result column="ysyj" property="ysyj" />
        <result column="ytzj" property="ytzj" />
        <result column="ytzj_start_time" property="ytzjStartTime" />
        <result column="ytzj_end_time" property="ytzjEndTime" />
        <result column="cqzj" property="cqzj" />
        <result column="cqzj_start_time" property="cqzjStartTime" />
        <result column="cqzj_end_time" property="cqzjEndTime" />
        <result column="ytyj" property="ytyj" />
        <result column="fmyj" property="fmyj" />
        <result column="hff" property="hff" />
        <result column="wyf" property="wyf" />
        <result column="wxj" property="wxj" />
        <result column="tnsf" property="tnsf" />
        <result column="tndf" property="tndf" />
        <result column="tnmq" property="tnmq" />
        <result column="wyj" property="wyj" />
        <result column="wlf" property="wlf" />
        <result column="sf" property="sf" />
        <result column="df" property="df" />
        <result column="qsf" property="qsf" />
        <result column="wxf" property="wxf" />
        <result column="mjf" property="mjf" />
        <result column="qtf" property="qtf" />
        <result column="wygtsf" property="wygtsf" />
        <result column="wygtdf" property="wygtdf" />
        <result column="zhfwf" property="zhfwf" />
        <result column="hj" property="hj" />
        <result column="stzj" property="stzj" />
        <result column="styj" property="styj" />
        <result column="sjfmyj" property="sjfmyj" />
        <result column="stshf" property="stshf" />
        <result column="cqzj_sj" property="cqzjSj" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        project_id, partition_id, source_id, order_id, out_or_change_time, yszj, yszj_start_time, yszj_end_time, ysyj, ytzj, ytzj_start_time, ytzj_end_time, cqzj, cqzj_start_time, cqzj_end_time, ytyj, fmyj, hff, wyf, wxj, tnsf, tndf, tnmq, wyj, wlf, sf, df, qsf, wxf, mjf, qtf, wygtsf, wygtdf, zhfwf, hj, stzj, styj, sjfmyj, stshf, cqzj_sj
    </sql>

    <select id="report" resultType="cn.uone.bean.entity.business.report.vo.ConfirmReleaseVo">
        select
        DATE_FORMAT(o.pay_time, '%Y-%m-%d') pay_time,
        DATE_FORMAT(ro.yszj_start_time, '%Y-%m-%d') yszj_start_time,
        DATE_FORMAT(ro.yszj_end_time, '%Y-%m-%d') yszj_end_time,
        DATE_FORMAT(ro.ytzj_start_time, '%Y-%m-%d') ytzj_start_time,
        DATE_FORMAT(ro.ytzj_end_time, '%Y-%m-%d') ytzj_end_time,
        DATE_FORMAT(ro.cqzj_start_time, '%Y-%m-%d') cqzj_start_time,
        DATE_FORMAT(ro.cqzj_end_time, '%Y-%m-%d') cqzj_end_time,
        DATE_FORMAT(ro.out_or_change_time, '%Y-%m-%d') out_or_change_time,
        ro.project_id,ro.partition_id,ro.source_id,ro.order_id,ro.yszj,ro.ysyj,ro.ytzj,ro.cqzj,ro.ytyj,ro.fmyj,ro.hff,ro.wyf,ro.wxj,ro.tnsf,ro.tndf,ro.tnmq,ro.wyj,ro.wlf,ro.sf,ro.df,ro.qsf,ro.wxf,ro.mjf,ro.qtf,ro.wygtsf,ro.wygtdf,ro.zhfwf,ro.hj,ro.stzj,ro.styj,ro.sjfmyj,ro.stshf,ro.cqzj_sj,
        CONCAT(s.projectName,'-',s.partitionName,'-',IF(s.source_type ='2',ifnull(car.num,s.code),s.code))code,
        c.contract_code,ci.name,o.code order_code,o.order_type,
        o.pay_state,c.is_organize,s.source_type,c.contract_type,
        c.id as contractId,
        nc.contract_code new_contract_code,r.new_contract_id,
        r.confirm_reason as memo
        from t_report_release_order ro
        INNER JOIN t_bil_order o on o.id = ro.order_id
        LEFT JOIN v_res_source s on s.id=o.source_id
        left join t_res_plan_partition pp on pp.id=s.partition_id
        LEFT JOIN t_cont_contract c on c.id=o.contract_id
        LEFT JOIN t_cont_contract_source_rel cs ON cs.contract_id = c.id AND cs.source_id = o.source_id
        LEFT JOIN t_base_car car ON car.contract_source_id = cs.id
        INNER JOIN t_cont_contract_info ci on ci.contract_id=c.id
        LEFT JOIN t_biz_release r on r.contract_id=o.contract_id  AND r.state != '9'
        LEFT JOIN t_cont_contract nc on nc.id=r.new_contract_id
        where 1=1
        <if test="po.projectId != null and po.projectId != '' ">
            AND s.project_id = #{po.projectId}
        </if>
        <if test="po.refundStartTime != null and po.refundStartTime != '' ">
            and date_format(o.pay_time,'%Y-%m-%d') &gt;= date_format(#{po.refundStartTime},'%Y-%m-%d')
        </if>
        <if test="po.refundEndTime != null and po.refundEndTime != '' ">
            and date_format(o.pay_time,'%Y-%m-%d') &lt;= date_format(#{po.refundEndTime},'%Y-%m-%d')
        </if>
        <if test="po.orderStartTime != null and po.orderStartTime != '' ">
            AND if(o.order_type='200', date_format(o.create_date,'%Y-%m-%d') &gt;= date_format(#{po.orderStartTime},'%Y-%m-%d'), date_format(r.checkout_date,'%Y-%m-%d') &gt;= date_format(#{po.orderStartTime},'%Y-%m-%d'))
        </if>
        <if test="po.orderEndTime != null and po.orderEndTime != '' ">
            AND if(o.order_type='200',  date_format(o.create_date,'%Y-%m-%d') &lt;= date_format(#{po.orderEndTime},'%Y-%m-%d'), date_format(r.checkout_date,'%Y-%m-%d') &lt;= date_format(#{po.orderEndTime},'%Y-%m-%d'))
        </if>
        <if test="po.propertyNature != null and po.propertyNature != '' ">
            and pp.property_nature=#{po.propertyNature}
        </if>
        <if test="po.partition != null and po.partition != '' ">
            and pp.id=#{po.partition}
        </if>
        <if test="po.orderType != null and po.orderType != '' ">
            and o.order_type=#{po.orderType}
        </if>
        <if test="po.payState != null and po.payState != '' ">
            and o.pay_state=#{po.payState}
        </if>
        <if test="po.platform != null and po.platform != ''">
            AND c.platform = #{po.platform}
        </if>
        order by s.partitionName,s.code asc
    </select>

    <select id="getReceivableList" resultType="cn.uone.bean.entity.business.report.vo.ReceivableVo">
        SELECT r.name,r.client_type clientType,boc.responsible_person responsiblePerson,boc.tel responsiblePersonTel,boc.unreceived_cause unreceivedCause,
        bi.tax_date taxDate, bi.tax_payment taxPayment,o.payment - bi.tax_payment AS notInvoicedAmount,
        o.payable_time as payableTime,o.actual_payment as actualPayment,o.payment,o.pay_time as payTime,o.order_type as orderType,
        CASE WHEN o.actual_payment is not null  THEN o.payment - o.actual_payment ELSE o.payment END as uncollected,
        CASE WHEN o.payable_time &lt; now()   THEN '到期' ELSE '未到期' END as urgeSign,
        CASE WHEN o.demand_payment_count is null  THEN '未催收' ELSE '已催收' END as urgeCase,
        CASE WHEN 1 = 1 THEN '漳州城投资产经营管理有限公司' END AS comeAndGo
        FROM t_bil_order o
        LEFT JOIN t_bil_order_collection boc on boc.order_id = o.id
        LEFT JOIN t_bil_invoice bi on bi.order_id = o.id
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN v_res_source s on o.source_id = s.id
        WHERE 1 = 1
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.name  != null and vo.name != ''">
            AND r.name  like CONCAT('%',#{vo.name},'%')
        </if>
        <if test="vo.orderType    != null and vo.orderType    != ''">
            AND o.order_type = #{vo.orderType}
        </if>
        <if test="vo.startPayableTime   != null ">
            AND date_format(o.payable_time,'%Y-%m-%d') &gt;= date_format(#{vo.startPayableTime},'%Y-%m-%d')
        </if>
        <if test="vo.endPayableTime   != null ">
            AND date_format(o.payable_time,'%Y-%m-%d') &lt;= date_format(#{vo.endPayableTime},'%Y-%m-%d')
        </if>
        <if test="vo.startPayTime   != null ">
            AND date_format(o.pay_time,'%Y-%m-%d') &gt;= date_format(#{vo.startPayTime},'%Y-%m-%d')
        </if>
        <if test="vo.endPayTime   != null ">
            AND date_format(o.pay_time,'%Y-%m-%d') &lt;= date_format(#{vo.endPayTime},'%Y-%m-%d')
        </if>
        AND  o.is_push = 1
    </select>


    <select id="getTotalMoney" resultType="java.lang.String">
        select CONCAT(
        SUM(CASE WHEN o.payment is null  THEN 0 ELSE o.payment END),',',
        SUM(CASE WHEN o.actual_payment is null  THEN 0 ELSE o.actual_payment END),',',
        SUM(CASE WHEN o.payment is null  THEN 0 ELSE o.payment END)-SUM(CASE WHEN o.actual_payment is null  THEN 0 ELSE o.actual_payment END)
        ) as total
        FROM t_bil_order o
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN v_res_source s on o.source_id = s.id
        WHERE 1 = 1
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.name  != null and vo.name != ''">
            AND r.name  like CONCAT('%',#{vo.name},'%')
        </if>
        <if test="vo.orderType    != null and vo.orderType    != ''">
            AND o.order_type = #{vo.orderType}
        </if>
        <if test="vo.startPayableTime   != null ">
            AND date_format(o.payable_time,'%Y-%m-%d') &gt;= date_format(#{vo.startPayableTime},'%Y-%m-%d')
        </if>
        <if test="vo.endPayableTime   != null ">
            AND date_format(o.payable_time,'%Y-%m-%d') &lt;= date_format(#{vo.endPayableTime},'%Y-%m-%d')
        </if>
        <if test="vo.startPayTime   != null ">
            AND date_format(o.pay_time,'%Y-%m-%d') &gt;= date_format(#{vo.startPayTime},'%Y-%m-%d')
        </if>
        <if test="vo.endPayTime   != null ">
            AND date_format(o.pay_time,'%Y-%m-%d') &lt;= date_format(#{vo.endPayTime},'%Y-%m-%d')
        </if>
        and  o.is_push = 1
    </select>

    <select id="getBillDetail" resultType="cn.uone.bean.entity.business.report.vo.BillDetailVo">
        SELECT
        o.id,
        Concat( s.source_name, s.partitionName ) AS assetName,
        s.area AS area,
        r.NAME AS tenant,
        r.tel AS tel,
        ( SELECT min( oi.start_time ) FROM t_bil_order_item oi WHERE oi.order_id = o.id ) AS startTime,
        ( SELECT max( oi.end_time ) FROM t_bil_order_item oi WHERE oi.order_id = o.id ) AS endTime,
        sr.orig_price AS monthlyRent,
        o.payment AS taxIncluded,
        c.tax_point AS taxPoint,
        ROUND( o.payment / ( ( IFNULL(c.tax_point,0) / 100 ) + 1 ), 2 ) AS taxExclusive,
        c.pay_type  AS payType
        FROM
        t_bil_order o
        LEFT JOIN t_cont_contract c ON c.id = o.contract_id
        LEFT JOIN t_cont_contract_source_rel sr ON sr.contract_id = o.contract_id
        LEFT JOIN v_res_source s ON o.source_id = s.id
        LEFT JOIN t_bil_invoice i ON i.order_id = o.id
        LEFT JOIN v_sys_renter r ON r.id = o.payer_id
        WHERE
        1 = 1
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.assetName  != null and vo.assetName != ''">
            AND (s.source_name  like CONCAT('%',#{vo.assetName},'%') or  s.partitionName  like CONCAT('%',#{vo.assetName},'%'))
        </if>
        <if test="vo.payType    != null and vo.payType    != ''">
            AND c.pay_type = #{vo.payType}
        </if>
        <if test="vo.tenant    != null and vo.tenant    != ''">
            AND r.name  like CONCAT('%',#{vo.tenant},'%')
        </if>
        AND o.is_push = 1
    </select>

    <!--押金意向金-->
    <select id="getDepositEarnest" resultType="cn.uone.bean.entity.business.report.vo.DepositEarnestVo">
        SELECT c.`contract_code` as contractCode,s.`projectName`,s.`partitionName`,s.code,s.area,c.`price`,
        DATE_FORMAT(t.`pay_time`,'%Y-%m-%d') as payTime,t.`order_type` as orderType,t.`bills_code` billsCode,t.`payment`,
        IF(t.pay_state !='20' OR o.pay_state IN ('20','70','90'),'是','否') ishs,ct.order_type orderTypes,
        ct.jzDate,ct.yearMonth jzYearMonth,ct.payment jzPayment,(t.payment-ct.payment) qmPayment
        FROM t_bil_order_item i,t_cont_contract c,v_res_source s,t_bil_order t
        LEFT JOIN t_bil_order o ON o.`contract_id`=t.`contract_id` AND o.`order_type` IN('130','140')
        LEFT JOIN (SELECT co.`from_order_id`,GROUP_CONCAT(cb.`order_type`) order_type,
        DATE_FORMAT(co.create_date,'%Y-%m-%d') jzDate,DATE_FORMAT(ci.`start_time`,'%Y年%m月') yearMonth,SUM(co.payment) payment
        FROM t_bil_carry_over co,t_bil_order cb,t_bil_order_item ci
        WHERE co.`order_item_id`=ci.id AND ci.`order_id`=cb.id GROUP BY co.`from_order_id`,DATE_FORMAT(ci.`start_time`,'%Y年%m月')) ct ON ct.from_order_id=t.id
        WHERE t.id=i.`order_id` AND t.`contract_id`=c.id AND t.`source_id`=s.`id` AND
        t.`order_type` IN ('5','10') AND t.`pay_state` IN('20','70','90')
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.partitionName  != null and vo.partitionName != ''">
            AND s.partitionName  = #{vo.partitionName}
        </if>
        <if test="vo.code  != null and vo.code != ''">
            AND s.code  = #{vo.code}
        </if>
        ORDER BY c.`contract_code`
    </select>

    <!--收入结转-->
    <select id="getIncomeCarryforward" resultType="cn.uone.bean.entity.business.report.vo.IncomeCarryforwardVo">
        SELECT c.`contract_code` as contractCode,s.`projectName`,s.`partitionName`,s.code,s.area,c.`price`,i.`order_item_type` as orderItemType,
        DATE_FORMAT(i.`start_time`,'%Y') as iyear,DATE_FORMAT(i.`start_time`,'%Y年%m月') as yearMonth,
        DATE_FORMAT(t.`pay_time`,'%Y年%m月') as payDate,SUM(i.payment) as  payablePayment,iao.`invoice_tax_rate` as invoiceTaxRate,
        SUM(i.`payment`)/(1+iao.`invoice_tax_rate`) as  notaxPayment,IF( t.invoice_state ='0','否','是') as invoiceState,
        bi.tax_number as taxNumber
        FROM t_bil_order_item i,t_cont_contract c,t_bil_order t
        LEFT JOIN v_res_source s ON s.id=t.`source_id`
        LEFT JOIN t_invoice_account_ordertype_rel iao ON iao.`order_type`=t.`order_type` AND iao.`project_id`=s.`project_id`
        LEFT JOIN t_bil_invoice bi ON bi.order_id=t.id
        WHERE t.id=i.`order_id` AND t.`contract_id`=c.id AND t.`pay_state` !='30'
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.partitionName  != null and vo.partitionName != ''">
            AND s.partitionName  = #{vo.partitionName}
        </if>
        <if test="vo.code  != null and vo.code != ''">
            AND s.code  = #{vo.code}
        </if>
        GROUP BY c.`contract_code`,i.`order_item_type`,DATE_FORMAT(i.`start_time`,'%Y年%m月')
        ORDER BY c.`contract_code`,DATE_FORMAT(i.`start_time`,'%Y年%m月')
    </select>


    <!--钱款报表-->
    <select id="getArrearage" resultType="cn.uone.bean.entity.business.report.vo.ArrearageVo">
        SELECT c.`contract_code` contractCode,s.`projectName`,s.`partitionName`,s.code,s.area,c.`price`,
        i.`order_item_type` orderItemType,DATE_FORMAT(t.`payable_time`,'%Y-%m-%d') payableDate,
        DATE_FORMAT(i.`start_time`,'%Y') iyear,DATE_FORMAT(i.`start_time`,'%Y年%m月') yearMonth,
        SUM(i.payment) payablePayment,SUM(IF(t.`pay_state` IN ('20','70','90'),i.`payment`,0)) actualPayment,
        SUM(IF(t.`pay_state`='10' AND DATEDIFF(CURDATE(), t.`payable_time`) &gt; 0 ,i.`payment`,0)) owePayment,
        IF(t.`pay_state`='10' AND DATEDIFF(CURDATE(), t.`payable_time`) &gt; 0 AND DATEDIFF(CURDATE(), t.`payable_time`)  &lt; 31,DATEDIFF(CURDATE(), t.`payable_time`),'') 'owe30',
        IF(t.`pay_state`='10' AND DATEDIFF(CURDATE(), t.`payable_time`) &gt; 30 AND DATEDIFF(CURDATE(), t.`payable_time`) &lt; 61,DATEDIFF(CURDATE(), t.`payable_time`),'') 'owe60',
        IF(t.`pay_state`='10' AND DATEDIFF(CURDATE(), t.`payable_time`) &gt; 60 AND DATEDIFF(CURDATE(), t.`payable_time`) &lt; 91,DATEDIFF(CURDATE(), t.`payable_time`),'') 'owe90',
        IF(t.`pay_state`='10' AND DATEDIFF(CURDATE(), t.`payable_time`) &gt; 90,DATEDIFF(CURDATE(), t.`payable_time`),'') 'owe90s'
        FROM t_bil_order t,t_bil_order_item i,t_cont_contract c,v_res_source s
        WHERE t.id=i.`order_id` AND t.`contract_id`=c.id AND t.`source_id`=s.`id` AND t.`pay_state` !='30'
        <if test="vo.projectId  != null and vo.projectId != ''">
            AND s.project_id  = #{vo.projectId}
        </if>
        <if test="vo.partitionName  != null and vo.partitionName != ''">
            AND s.partitionName  = #{vo.partitionName}
        </if>
        <if test="vo.code  != null and vo.code != ''">
            AND s.code  = #{vo.code}
        </if>
        GROUP BY c.`contract_code`,i.`order_item_type`,DATE_FORMAT(i.`start_time`,'%Y年%m月')
        ORDER BY c.`contract_code`,DATE_FORMAT(i.`start_time`,'%Y年%m月')
    </select>

    <!--租金收入-->
    <select id="getRentalIncome" resultType="cn.uone.bean.entity.business.report.vo.RentalIncomeVo">
        SELECT o.id,o.payment,
               CONCAT_WS('至',DATE_FORMAT(MIN(i.start_time),'%Y-%m-%d'),
                         DATE_FORMAT(MAX(i.end_time),'%Y-%m-%d')) AS period,
               CONCAT_WS('-', s.projectName, s.partitionName, IF(s.source_type='2',(SELECT bc.num FROM t_base_car bc,t_cont_contract_source_rel sr
                                                                                    WHERE sr.id=bc.contract_source_id AND sr.contract_id=c.id LIMIT 1),s.code )) AS houseName,
               r.`name` AS renter ,o.remark,tbi.tax_date AS invoiceDate,tbi.tax_number AS invoiceNumber,
               ROUND(tbi.tax_payment/(1+ rpi.param_value) ,2) AS noTaxPrice,
               ROUND(tbi.tax_payment*rpi.param_value,2) AS taxAmount,
               tbi.tax_payment AS totalInvoiceAmount,
               rpi.param_value AS taxRate,
               DATE_FORMAT(MIN(i.start_time),'%Y-%m') AS collectionMonth,
               c.pay_type AS monthQuantity,
               ROUND(c.price/(1+ rpi.param_value) ,2) AS monthlyRentNoTax
        FROM t_bil_order o
                 LEFT JOIN t_bil_order_item i ON i.`order_id`=o.id
                 LEFT JOIN t_cont_contract c ON o.contract_id = c.id
                 LEFT JOIN (SELECT * FROM t_cont_contract_source_rel csr GROUP BY csr.contract_id)csr ON csr.contract_id=c.id
                 LEFT JOIN t_cont_rent_ladder l ON l.contract_source_id=csr.id AND o.create_date BETWEEN l.start_date AND l.end_date
                 LEFT JOIN v_res_source s ON o.source_id = s.id
                 LEFT JOIN t_res_plan_partition p ON s.partition_id = p.id
                 LEFT JOIN v_sys_renter r ON r.id = o.payer_id
                 LEFT JOIN t_cont_contract_info r2 ON r2.contract_id = c.id
                 LEFT JOIN t_base_enterprise e ON e.renter_id=c.signer_id
                 LEFT JOIN t_bil_discount_log bdl ON  bdl.order_id=o.id
                 LEFT JOIN t_bil_discount d ON d.id=bdl.discount_id
                 LEFT JOIN t_bil_transfer bt ON bt.order_id=o.id
                 LEFT JOIN t_bil_invoice tbi ON tbi.order_id = o.id
                 LEFT JOIN t_res_project_para rpi ON rpi.`project_id`=s.`project_id` AND rpi.`param_code`='XMCS_0101'
        WHERE 1=1 AND o.payment != 0 AND o.pay_state NOT IN('40')
        AND o.order_type ='20'
        AND YEAR(i.start_time) &gt;= #{vo.year}
        AND s.project_id  = #{vo.projectId}
        GROUP BY o.id
    </select>

    <select id="getRentalIncomeForZzct" resultType="cn.uone.bean.entity.business.report.vo.RentalIncomeVo">
        SELECT o.id,o.payment,
               CONCAT_WS('至',DATE_FORMAT(MIN(i.start_time),'%Y-%m-%d'),
                         DATE_FORMAT(MAX(i.end_time),'%Y-%m-%d')) AS period,
               CONCAT_WS('-', s.projectName, s.partitionName, IF(s.source_type='2',(SELECT bc.num FROM t_base_car bc,t_cont_contract_source_rel sr
                                                                                    WHERE sr.id=bc.contract_source_id AND sr.contract_id=c.id LIMIT 1),s.code )) AS houseName,
               r.`name` AS renter ,o.remark,tbi.tax_date AS invoiceDate,tbi.tax_number AS invoiceNumber,
               ROUND(tbi.tax_payment/(1+ c.tax_point*0.01) ,2) AS noTaxPrice,
               ROUND(tbi.tax_payment*c.tax_point*0.01,2) AS taxAmount,
               tbi.tax_payment AS totalInvoiceAmount,
               c.tax_point*0.01 AS taxRate,
               DATE_FORMAT(MIN(i.start_time),'%Y-%m') AS collectionMonth,
               c.pay_type AS monthQuantity,
               ROUND(c.price/(1+ c.tax_point*0.01) ,2) AS monthlyRentNoTax
        FROM t_bil_order o
                 LEFT JOIN t_bil_order_item i ON i.`order_id`=o.id
                 LEFT JOIN t_cont_contract c ON o.contract_id = c.id
                 LEFT JOIN (SELECT * FROM t_cont_contract_source_rel csr GROUP BY csr.contract_id)csr ON csr.contract_id=c.id
                 LEFT JOIN t_cont_rent_ladder l ON l.contract_source_id=csr.id AND o.create_date BETWEEN l.start_date AND l.end_date
                 LEFT JOIN v_res_source s ON o.source_id = s.id
                 LEFT JOIN t_res_plan_partition p ON s.partition_id = p.id
                 LEFT JOIN v_sys_renter r ON r.id = o.payer_id
                 LEFT JOIN t_cont_contract_info r2 ON r2.contract_id = c.id
                 LEFT JOIN t_base_enterprise e ON e.renter_id=c.signer_id
                 LEFT JOIN t_bil_discount_log bdl ON  bdl.order_id=o.id
                 LEFT JOIN t_bil_discount d ON d.id=bdl.discount_id
                 LEFT JOIN t_bil_transfer bt ON bt.order_id=o.id
                 LEFT JOIN t_bil_invoice tbi ON tbi.order_id = o.id
        WHERE 1=1 AND o.payment != 0 AND o.pay_state NOT IN('40')
        AND o.order_type ='20'
        AND YEAR(i.start_time) &gt;= #{vo.year}
          AND s.project_id  = #{vo.projectId}
        GROUP BY o.id
    </select>

</mapper>
