package cn.uone.crm.service.impl;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.crm.dao.RenterDao;
import cn.uone.crm.service.IRenterService;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 *  租客用户
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-14
 */
@Service
public class RenterServiceImpl extends ServiceImpl<RenterDao, RenterEntity> implements IRenterService {

    private static final Logger log = LoggerFactory.getLogger(RenterServiceImpl.class);

    @Resource
    private RenterDao renterDao;

    /**
     * 参数判断;
     * 新增租客用户;
     */
    @Transactional
    @Override
    public int addRenter(RenterEntity renterEntity) throws Exception {
        // 参数判断

        // 新增租客用户
        int i;

        try {
            i = renterDao.insert(renterEntity);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("新增租客用户发生异常");
        }

        if (i < 1) {
            throw new Exception("新增租客失败");
        }

        return i;
    }

    /**
     * 参数判断;
     * 获取租客用户信息;
     */
    @Override
    public RenterEntity getRenterById(String id) throws BusinessException {
        // 参数判断
        if (StrUtil.isBlank(id)) {
            throw new BusinessException("参数异常");
        }

        // 获取租客用户信息
        RenterEntity renterEntity;

        try {
            renterEntity = renterDao.selectById(id);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("获取租客用户信息发生异常");
        }

        return renterEntity;
    }

    @Override
    public int queryAgreement(String renterId) throws BusinessException {
        RenterEntity renterEntity = getRenterById(renterId);
        if(renterEntity !=null){
            return renterEntity.getAgreementState();
        }
        return 0;
    }

    @Override
    public RenterEntity getRenterByTel(String tel) {
        // 获取租客用户信息
        if (StrUtil.isNotBlank(tel)) {
            tel = tel.trim();
        }
        List<RenterEntity> renters = baseMapper.selectList(new QueryWrapper<RenterEntity>().eq("tel", tel));
        RenterEntity renterEntity = null;
        if (null != renters && renters.size() > 0) {
            renterEntity = renters.get(0);
        }
        return renterEntity;
    }

    @Override
    public RenterEntity getRenterByOpenId(String openId) {
        List<RenterEntity> renters = baseMapper.selectList(new QueryWrapper<RenterEntity>().eq("openid", openId));
        RenterEntity renterEntity = null;
        if (null != renters && renters.size() > 0) {
            renterEntity = renters.get(0);
        }
        return renterEntity;
    }

    @Override
    public RenterEntity getRenterByUsername(String username,String type) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        RenterEntity renterEntity = baseMapper.selectOne(new QueryWrapper<RenterEntity>().eq("username", username).eq("type", type));
        return renterEntity;

    }

    @Override
    public IPage<RenterEntity> findByCondition(Page page, Map map) {
        return baseMapper.selectRenterByMap(page, map);
    }



    @Override
    public RenterEntity getRenterByTelAndType(String tel, String type) {
        if (StrUtil.isBlank(tel)) {
            return null;
        }
        List<RenterEntity> renterEntity = baseMapper.selectList(new QueryWrapper<RenterEntity>().eq("tel", tel).eq("type", type));
        return renterEntity.size()>0?renterEntity.get(0):null;

    }

    @Override
    public void updateId(String newId, String oldId) {
        renterDao.updateId(newId,oldId);
    }

    @Override
    public RenterEntity queryByUserName(String userName, String type) {
        Map<String, Object> query = new HashMap<>();
        query.put("username",userName);
        query.put("type",type);
        return baseMapper.selectRenterByMap(query);
    }

    @Override
    public void setOpenidNULL(String id) {
        baseMapper.setOpenidNULL(id);
    }

    @Override
    public List<Map<String, Object>> getAll() {
        return baseMapper.getAll();
    }
}
