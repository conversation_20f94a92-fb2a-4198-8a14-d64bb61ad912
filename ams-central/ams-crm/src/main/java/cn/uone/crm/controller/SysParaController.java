package cn.uone.crm.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.crm.SysParaEntity;
import cn.uone.crm.service.ISysParaService;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import feign.Body;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@RestController
@RequestMapping("/sys/para")
public class SysParaController extends BaseController implements ISysParaFegin {

    @Autowired
    private ISysParaService sysParaService;

    @RequestMapping("/queryPage")
    @RequiresPermissions("sys:para:select")
    public RestResponse queryPage(Page page, String name) {
        QueryWrapper<SysParaEntity> wrapper = new QueryWrapper();
        if (StrUtil.isNotBlank(name)) {
            wrapper.and(wrapper1 -> wrapper1.like("param_code", name).or().eq("param_name", name));
        }
        IPage<SysParaEntity> ipage = sysParaService.page(page, wrapper);
        return RestResponse.success().setData(ipage);
    }

    @Override
    @RequestMapping("/saveOrUpdate")
    public RestResponse saveOrUpdate(@RequestBody SysParaEntity entity) {
        if (entity != null) {
            sysParaService.saveOrUpdate(entity);
        } else {
            return RestResponse.failure("没有正确传入数据");
        }
        return RestResponse.success();
    }

    @RequestMapping("/updateQuery")
    public RestResponse updateQuery(String id) {
        SysParaEntity entity = sysParaService.getById(id);
        return RestResponse.success().setData(entity);
    }

    @Override
    @RequestMapping("/getByCode")
    public String getByCode(String code) {
        return sysParaService.getByCode(code);
    }

    @Override
    @RequestMapping("/getSysParaByCode")
    public SysParaEntity getSysParaByCode(String code) {
        return sysParaService.getSysParaByCode(code);
    }

    @RequestMapping("/del")
    public RestResponse del(String id) {
        sysParaService.removeById(id);
        return RestResponse.success();
    }
}
