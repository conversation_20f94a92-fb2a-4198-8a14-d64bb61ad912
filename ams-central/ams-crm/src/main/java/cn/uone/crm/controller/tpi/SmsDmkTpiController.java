package cn.uone.crm.controller.tpi;

import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.bean.entity.business.sys.SysInterfaceMsgEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.crm.service.IExpenseConfigService;
import cn.uone.crm.service.ISysCompanyService;
import cn.uone.fegin.sys.ISysInterfaceMsgFegin;
import cn.uone.fegin.tpi.ISmsAliFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("tpi/smsDmk")
public class SmsDmkTpiController {

    @Autowired
    ISmsAliFegin smsAliFegin;
    @Autowired
    IExpenseConfigService expenseConfigService;
    @Autowired
    ISysCompanyService sysCompanyService;
    @Autowired
    ISysInterfaceMsgFegin sysInterfaceMsgFegin;

    /**
     * 申请短信模板
     * @param saasCompanyId 公司id，由服务商提供
     * @param notifyUrl
     * @param templateType 短信类型。取值： 0：验证码。1：短信通知。2：推广短信。3：国际/港澳台消息。
     * @param templateName
     * @param templateContent
     * @param templateRule 模板变量规则。示例值 {"code":"characterWithNumber"}
     * @return
     * @throws Exception
     */
    @PostMapping("/createSmsTemplate")
    public RestResponse createSmsTemplate(@RequestParam("saasCompanyId") String saasCompanyId,
                                          @RequestParam(value = "notifyUrl",required = false)String notifyUrl,
                                          @RequestParam("templateType")Integer templateType,
                                          @RequestParam("templateName")String templateName,
                                          @RequestParam("templateContent")String templateContent,
                                          @RequestParam("templateRule")String templateRule) {
        try {
            SysCompanyEntity companyEntity = sysCompanyService.getById(saasCompanyId);
            String expenseProjectId = companyEntity.getTopId();
            JSONObject configEntity = expenseConfigService.getExpenseConfigByPro(expenseProjectId, ApiTypeEnum.SMS_ALI.getValue());
            String accessKeyId = configEntity.getStr("accessKeyId");
            String secretAccessKey = configEntity.getStr("secretAccessKey");
            String signName = configEntity.getStr("signName");
            return smsAliFegin.createSmsTemplate(accessKeyId,secretAccessKey,notifyUrl, signName,templateType, templateName, templateContent, templateRule);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    /**
     * 查询模板审核状态
     * @param saasCompanyId
     * @param templateCode
     * @return
     * @throws Exception
     */
    @PostMapping("/getSmsTemplate")
    public RestResponse getSmsTemplate(@RequestParam("saasCompanyId")String saasCompanyId,
                                       @RequestParam("templateCode")String templateCode) {
        try {
            SysCompanyEntity companyEntity = sysCompanyService.getById(saasCompanyId);
            String expenseProjectId = companyEntity.getTopId();
            JSONObject configEntity = expenseConfigService.getExpenseConfigByPro(expenseProjectId, ApiTypeEnum.SMS_ALI.getValue());
            String accessKeyId = configEntity.getStr("accessKeyId");
            String secretAccessKey = configEntity.getStr("secretAccessKey");
            return smsAliFegin.getSmsTemplate(accessKeyId,secretAccessKey,templateCode);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    /**
     * 删除短信模板
     * @param saasCompanyId
     * @param templateCode
     * @return
     * @throws Exception
     */
    @PostMapping("/deleteSmsTemplate")
    public RestResponse deleteSmsTemplate(@RequestParam("saasCompanyId")String saasCompanyId,
                                          @RequestParam("templateCode")String templateCode) {
        try {
            SysCompanyEntity companyEntity = sysCompanyService.getById(saasCompanyId);
            String expenseProjectId = companyEntity.getTopId();
            JSONObject configEntity = expenseConfigService.getExpenseConfigByPro(expenseProjectId, ApiTypeEnum.SMS_ALI.getValue());
            String accessKeyId = configEntity.getStr("accessKeyId");
            String secretAccessKey = configEntity.getStr("secretAccessKey");
            return smsAliFegin.deleteSmsTemplate(accessKeyId,secretAccessKey,templateCode);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    /**
     * 查询短信发送详情
     * @param saasCompanyId
     * @param phoneNumber
     * @param bizId 发送回执 ID，即发送流水号。调用 SendSms 或 SendBatchSms 发送短信时，返回值中的 BizId 字段。
     * @param sendDate 短信发送日期，支持查询最近 30 天的记录。
     * @return
     * @throws Exception
     */
    @PostMapping("/querySendDetails")
    public RestResponse querySendDetails(@RequestParam("saasCompanyId")String saasCompanyId,
                                         @RequestParam("phoneNumber")String phoneNumber,
                                         @RequestParam(value = "bizId",required = false)String bizId,
                                         @RequestParam("sendDate")String sendDate) {
        try {
            SysCompanyEntity companyEntity = sysCompanyService.getById(saasCompanyId);
            String expenseProjectId = companyEntity.getTopId();
            JSONObject configEntity = expenseConfigService.getExpenseConfigByPro(expenseProjectId, ApiTypeEnum.SMS_ALI.getValue());
            String accessKeyId = configEntity.getStr("accessKeyId");
            String secretAccessKey = configEntity.getStr("secretAccessKey");
            return smsAliFegin.querySendDetails(accessKeyId,secretAccessKey,phoneNumber,bizId,sendDate);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    /**
     * 阿里云短信模板申请状态回调
     * @param jsonArray
     * @return
     */
    @PostMapping("/templateSmsReport")
    @UonePermissions
    public RestResponse templateSmsReport(@RequestBody JSONArray jsonArray) {
        JSONObject jsonObject = jsonArray.getJSONObject(0);
        String templateCode = jsonObject.getStr("template_code");
        SysInterfaceMsgEntity entity = sysInterfaceMsgFegin.getByApiTypeAndNotifyId(ApiTypeEnum.SMS_ALI.getValue(),templateCode,"阿里云短信申请模板");
        if (entity == null) {
            return RestResponse.failure("未找到模板申请记录").setAny("msg","接收失败");
        }
        String notifyUrl = entity.getNotifyUrl();
        String res = HttpRequest.post(notifyUrl).body(jsonObject.toString())
                .execute().body();
        JSONObject resJson = JSONUtil.parseObj(res);
        Integer code = resJson.getInt("code");
        if(code != 0){
            String message = jsonObject.getStr("msg");
            return RestResponse.failure(message).setAny("msg","接收失败");
        }
        return RestResponse.success().setAny("code",0).setAny("msg","接收成功");
    }

    /**
     * 阿里云短信发送状态回调
     * @param jsonArray
     * @return
     */
    @PostMapping("/smsReport")
    @UonePermissions
    public RestResponse smsReport(@RequestBody JSONArray jsonArray) {
        boolean isFail = false;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String bizId = jsonObject.getStr("biz_id");
            SysInterfaceMsgEntity entity = sysInterfaceMsgFegin.getByApiTypeAndNotifyId(ApiTypeEnum.SMS_ALI.getValue(),bizId,"阿里云短信发送");
            if (entity == null) {
                continue;
            }
            String notifyUrl = entity.getNotifyUrl();
            String res = HttpRequest.post(notifyUrl).body(jsonObject.toString())
                    .execute().body();
            JSONObject resJson = JSONUtil.parseObj(res);
            Integer code = resJson.getInt("code");
            if(code != 0){
                String message = jsonObject.getStr("msg");
                Console.log("阿里云请求回调地址{}失败：{}",notifyUrl,message);
                isFail = true;
            }
        }
        if(isFail){
            return RestResponse.failure("接收成功").setAny("msg","接收成功");
        }
        return RestResponse.success().setAny("code",0).setAny("msg","接收成功");
    }

    @PostMapping("/smsNotify")
    @UonePermissions
    public RestResponse smsNotify(@RequestBody JSONObject jsonObject) {
        Console.log("阿里云回调：{}",jsonObject.toString());
        return RestResponse.success().setAny("code",0).setAny("msg","接收成功");
    }

}
