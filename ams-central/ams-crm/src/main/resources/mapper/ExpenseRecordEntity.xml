<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.TExpenseRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.ExpenseRecordEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="expense_type" property="expenseType" />
        <result column="expenser_id" property="expenserId" />
        <result column="expenser_name" property="expenserName" />
        <result column="expense_amount" property="expenseAmount" />
        <result column="expense_balance" property="expenseBalance" />
        <result column="expense_time" property="expenseTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        expense_type, expenser_id, expenser_name, expense_amount, expense_balance, expense_time, remark
    </sql>

    <select id="selectPage" resultMap="BaseResultMap">
        SELECT t.* from t_expense_record t
        WHERE 1=1 and t.expenser_id = #{searchVo.expenserId}
        <if test="searchVo.expenseType != null and searchVo.expenseType != ''">
            AND t.expense_type = #{searchVo.expenseType}
        </if>
        <if test="searchVo.startDate != null and searchVo.startDate != ''">
            AND <![CDATA[date(t.expense_time) >= date(#{searchVo.startDate}) ]]>
        </if>
        <if test="searchVo.endDate != null and searchVo.endDate != ''">
            AND <![CDATA[date(t.expense_time) <= date(#{searchVo.endDate}) ]]>
        </if>
        order by t.create_date desc
    </select>

</mapper>
