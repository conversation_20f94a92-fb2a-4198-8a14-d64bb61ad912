package cn.uone.bean.constant;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

/**
 * Created by xmlin on 2018-12-13.
 */

@Data
public class ContTempParamConstants{

    private ContTempParamConstants(){}

    //合同编码
    public String CONT_CODE = "____________";

    //签约日期
    public String SIGN_DATE = "____________";

    //乙方(承租方)
    public String PB_NAME="__________";
    //乙方联系电话
    public String PB_TEL="__________";
    //乙方联系地址
    public String PB_ADD="__________";
    //乙方身份证号码/同意社会信用代码
    public String PB_CODE="__________";

    //租赁房源
    public String RES_NAME="________________";

    //房屋用途
    public String RES_USE="乙方自住";

    //商业房源用途
    public String RES_USE_BUS ="________";

    //房屋面积
    public String RES_AREA="____㎡";

    //租期
    public String RES_TERM="从____年____月____日开始，至____ 年 ____月____日结束。";

    //经营品牌
    public String RES_BUS_BRAND="____________";

    //经营范围
    public String RES_BUS_SCOPE="____________";

    //车辆位置
    private String PB_CAR_ADD="停车场";

    //车牌号
    public String PB_CAR_NUM="____________";

    //车辆所有人
    public String PB_CAR_OWNER="____________";

    //车辆使用人
    public String PB_CAR_USER="____________";

    //乙方类型
    public String PB_CAR_CUS_TYPE="____________";

    //车位用途
    public String PB_CAR_USE="乙方自用";

    //车位系统编码
    public String PB_CAR_SYS_CODE="非固定指定车位";

    //车位租期
    public String PB_CAR_TERM="从____年____月____日开始，至____ 年 ____月____日结束。";

    //车位数量
    public String PB_CAR_QUA="____";

    //乙方身份证复印件
    public String CAR_PB_CODE_FILE="!signingzHtml!";

    //乙方行驶证复印件
    public String CAR_PB_LIC="!signingxsHtml!";

    //乙方驾驶证复印件
    public String CAR_PB_DO_LIC="!signingjsHtml!";

    //入住人员
    public String RENT_NAME="_________________";

    //紧急联系人
    public String RENT_URGENT="__________";

    //紧急联系人电话
    public String RENT_URGENT_TEL="__________";

    //设备清单
    public String DEV_LIST="!deviceTable!";

    //租赁房屋平面图
    public String PLAN_IMAGE="{houseTypeImg!}";

    //乙方法人代表身份证复印件
    public String BUS_PB_CODE_FILE="{renter.urgentTel!}";

    //乙方营业执照
    public String BUS_PB_LIC="{renter.urgentTel!}";

    //甲方收款户名
    public String PA_REC_NAME="__________";

    //甲方收款开户行
    public String PA_REC_BANK="__________";

    //甲方收款账号
    public String PA_REC_ACCOUNT="__________";

    public String STOP_RENT="{endYear!}年{endMonth!}月{endDay!}";

    public String HOUOSE_FORWARD="<p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><span style=\"font-family: 宋体, SimSun;\"><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体;\">甲方于</span><span style=\"text-decoration-line: underline; font-family: 宋体; line-height: 21px; font-size: 14px;\"></span><span style=\"font-family: 宋体;\">{signYear!}年{signMonth!}月{signDay!}日</span><span style=\"font-family: 宋体; line-height: 21px; font-size: 14px;\">与乙方签订了《{contractTitle!}》（合同编号：</span><span style=\"text-decoration-line: underline; line-height: 21px; font-size: 14px; font-family: 宋体;\">{contractCode!}</span><span style=\"font-family: 宋体; line-height: 21px; font-size: 14px;\">，以下简称“原合同”），乙方租赁房屋{community!}{roomCode!}</span><span style=\"text-decoration-line: underline; font-family: 宋体; line-height: 21px; font-size: 14px;\"></span><span style=\"font-family: 宋体;\">{roomCode!}</span><span style=\"text-decoration-line: underline; font-family: 宋体; line-height: 21px; font-size: 14px;\"></span><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体;\">房间）作为乙方自住之用，租赁期限为</span><span style=\"text-decoration-line: underline; font-family: 宋体; line-height: 21px; font-size: 14px;\"></span><span style=\"font-family: 宋体;\">{sYear!}年{sMonth!}月{sDay!}日起{eYear!}年{eMonth!}月{eDay!}日止</span><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体;\">。现乙方因以下原因与甲方提前终止租赁关系：</span></span></p><p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><br/></p><p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><span style=\"font-family: 宋体, SimSun;\"></span><span style=\"font-family: 宋体, SimSun;\"><strong>!checkouttype1! 乙方因&nbsp;</strong></span><span style=\"text-decoration-line: underline; font-family: 宋体, SimSun;\"><strong>&nbsp; &nbsp;{changeReason!}&nbsp;&nbsp;</strong></span><span style=\"font-family: 宋体, SimSun;\"><strong><strong><span style=\"font-family: 宋体; line-height: 21px; font-size: 14px;\">变更签约房间；</span></strong></strong></span><span style=\"font-family: 宋体, SimSun;\"><strong><strong><span style=\"font-family: 宋体; line-height: 21px; font-size: 14px;\"></span></strong></strong></span></p><p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><span style=\"font-family: 宋体, SimSun;\"><strong><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体;\"></span></strong></span></p><p style=\"font-family: sans-serif; white-space: normal;\"><span style=\"font-family: 宋体, SimSun;\"><strong>&nbsp; !checkouttype2! 乙方因&nbsp;</strong></span><span style=\"font-family: 宋体, SimSun; text-decoration-line: underline;\"><strong>&nbsp; &nbsp;<strong style=\"text-indent: 28px;\">{checkOutReason!}</strong><strong style=\"text-indent: 28px;\">&nbsp;</strong></strong></span><span style=\"font-family: 宋体, SimSun;\"><strong><strong style=\"text-indent: 28px;\"><span style=\"font-family: 宋体; line-height: 21px; font-size: 14px;\">变更住户；</span></strong></strong></span></p><p style=\"font-family: sans-serif; white-space: normal;\"><span style=\"font-family: 宋体, SimSun;\"><strong>&nbsp; !checkouttype3! 乙方违约提前退房。</strong></span></p><p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><span style=\"font-family: 宋体, SimSun;\"><strong><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体;\"><br/></span></strong></span></p><p style=\"font-family: sans-serif; white-space: normal; text-indent: 28px; line-height: 24px;\"><span style=\"line-height: 21px; font-size: 14px; font-family: 宋体, SimSun;\">甲乙双方就提前终止租赁关系相关事宜达成如下一致条款：</span></p><p><br/></p>";

    public String CAR_FORWARD ="<p style=\"font-family: sans-serif; white-space: normal; line-height: 24px; text-indent: 32px;\"><span style=\"font-family: 宋体;\">甲方授权委托代理方于{signYear!}年{signMonth!}月{signDay!}日与乙方签订了《{contractTitle!}》（合同编号：{contractCode!}，以下简称“原合同”），乙方承租{community!}项目车位车牌号为：<span style=\"font-weight: 700;\">{car.num!}</span>车辆所有人：{car.owner!}&nbsp;车辆使用人：{car.user}&nbsp;，作为小型轿车停放之用，租赁期限为{sYear!}年<span style=\"line-height: 24px;\">{sMonth!}</span>月<span style=\"line-height: 24px;\">{sDay!}</span>日起<span style=\"font-size: 14px;\">{eYear!}年{eMonth!}月{eDay!}日</span>止。现乙方因以下原因与甲方提前终止租赁关系：</span></p><p style=\"font-family: sans-serif; white-space: normal; line-height: 24px; text-indent: 27px;\"><span style=\"font-family: 宋体;\">&nbsp;<span style=\"line-height: 24px;\">{checkoutReason!}</span></span></p><p style=\"font-family: sans-serif; white-space: normal; line-height: 24px; text-indent: 32px;\"><span style=\"line-height: 24px; font-family: 宋体;\">甲乙双方就提前终止租赁关系相关事宜达成如下一致条款：</span></p>";


    public static ContTempParamConstants getInstance(){
        return new ContTempParamConstants();
    }

    public static JSONObject getJSON(){
        return JSONUtil.parseObj(getInstance());
    }

}
