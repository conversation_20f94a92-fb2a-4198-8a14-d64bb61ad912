package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_equipment_warn")
public class EquipmentWarnEntity extends BaseModel<EquipmentWarnEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *  设备编号
     */
    @TableField("device_no")
    private String deviceNo;

    /**
     *  设备类型 1  门锁 2 电表 3 水表
     */
    @TableField("type")
    private String type;

    /**
     *  预警状态 1 生效  2离线后上线不查询出来
     */
    @TableField("state")
    private String state;

    /**
     *  预警类型 1 离线 2 电量低 3 其它
     */
    @TableField("status")
    private String status;

    /**
     *  设备厂商 1 云丁 2 蛋呗
     */
    @TableField("types")
    private String types;

    /**
     * 事件发生事件
     */
    private Date occurTime;


    @Override
    public Serializable pkVal() {
        return id;
    }

    /**
     *  房源id
     */
    @TableField("source_id")
    private String sourceId;

}
