package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_cost_configure_detail")
public class ResCostConfigureDetailEntity extends BaseModel<ResCostConfigureDetailEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("cost_configure_id")
    private String costConfigureId;

    /**
     * 费用类型
     */
    @TableField("cost_type")
    private String costType;

    /**
     * 缴费主体
     */
    @TableField("pay_target")
    private String payTarget;

    /**
     * 收费主体
     */
    @TableField("charge_target")
    private String chargeTarget;

    /**
     * 缴费标准
     */
    @TableField("pay_standard")
    private String payStandard;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
