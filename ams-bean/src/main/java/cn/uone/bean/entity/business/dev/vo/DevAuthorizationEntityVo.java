package cn.uone.bean.entity.business.dev.vo;

import cn.uone.bean.entity.business.dev.DevAuthorizationEntity;

public class DevAuthorizationEntityVo extends DevAuthorizationEntity {

    private String realName;//授权人姓名
    private String userName;//被授权人姓名
    private String tel;//被授权人电话
    private String summary;

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
}
