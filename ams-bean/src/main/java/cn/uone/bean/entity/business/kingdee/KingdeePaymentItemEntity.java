package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_payment_item")
public class KingdeePaymentItemEntity extends BaseModel<KingdeePaymentItemEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("payment_id")
    private String paymentId;

    /**
     * 费用类型
     */
    @TableField("expense_type")
    private String expenseType;

    /**
     * 费用说明
     */
    @TableField("cost_description")
    private String costDescription;

    /**
     * 客户段
     */
    @TableField("customer")
    private String customer;

    /**
     * 应付金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 租客姓名
     */
    @TableField("customer_str")
    private String customerStr;

    /**
     * 房间编号
     */
    @TableField("room_number")
    private String roomNumber;

    /**
     * 子帐单类型文本
     */
    @TableField("cz_type")
    private String czType;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
