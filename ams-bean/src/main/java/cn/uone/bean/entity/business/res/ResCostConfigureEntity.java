package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_cost_configure")
public class ResCostConfigureEntity extends BaseModel<ResCostConfigureEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("project_id")
    private String projectId;

    /**
     * 费用配置名称
     */
    @TableField("name")
    private String name;

    /**
     * 费用配置类型
     */
    @TableField("type")
    private String type;

    /**
     * 说明
     */
    @TableField("illustration")
    private String illustration;

    /**
     * 房源类别
     */
    @TableField("source_type")
    private String sourceType;
    /**
     * 是否短租
     */
    @TableField("is_short")
    private Integer isShort;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
