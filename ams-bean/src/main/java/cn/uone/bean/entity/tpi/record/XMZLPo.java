package cn.uone.bean.entity.tpi.record;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XMZLPo {

    String ID;

    /**
     * 挂牌状态
     * 3	发布状态
     * 4	下架状态
     * 5	纳统
     */
    String GPZT;

    /**
     * 发布标题
     */
    String FBBT;

    /**
     * 校验时间
     */
    String JYSJ;

    /**
     * 房屋来源
     */
    String FWLY;

    /**
     * 房屋坐落
     */
    String FWZL;

    /**
     * 行政区
     */
    String XZQ;

    /**
     * 街道
     */
    String JD;

    /**
     * 小区名称
     */
    String XQMC;

    /**
     * 月租金
     */
    BigDecimal YZJ;
    /**
     * 挂牌类型
     */
    String  GPLX;
    /**
     * 出租面积
     */
    BigDecimal CZMJ;

    /**
     * 支付方式
     */
    String ZFFS;

    /**
     * 户型室
     */
    String  HXS;
    /**
     * 户型厅
     */
    String HXT;
    /**
     * 户型卫
     */
    String HXW;
    /**
     * 户型厨
     */
    String HXC;
    /**
     * 楼房性质
     */
    String LFXZ;
    /**
     * 朝向
     */
    String CX;

    /**
     * 总层数
     */
    Integer ZCS;
    /**
     * 所在层
     */
    Integer  SZC;
    /**
     *  装修程度
     */
    String  ZXCD;
    /**
     * 联系人姓名
     */
    String LXRMC;
    /**
     * 联系人电话
     */
    String LXRDH;
    /**
     * 配套设施
     */
    String PTSS;

    /**
     * 房源描述
     */
    String FYMS;

    /**
     * 图片
     */
    List<String> TPLIST;

    /**
     * 房屋用途
     */
    String FWYT = "01";
}
