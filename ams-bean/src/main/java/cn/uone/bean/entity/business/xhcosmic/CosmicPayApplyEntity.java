package cn.uone.bean.entity.business.xhcosmic;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;

import cn.uone.web.base.BaseModel;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.compress.utils.Lists;

/**
 * <p>
 * 金蝶(星瀚)系统 付款申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_pay_apply")
public class CosmicPayApplyEntity extends BaseModel<CosmicPayApplyEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 付款申请单编码
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 付款处理单编码
     */
    @TableField("deal_bill_no")
    private String dealBillNo;

    /**
     * 凭证号
     */
    @TableField("voucher_num")
    private String voucherNum;

    /**
     * 来源系统
     */
    @TableField("source_system")
    private String sourceSystem;

    /**
     * 来源单据类型
     */
    @TableField("source_bill_type")
    private String sourceBillType;

    /**
     * 来源单据编号
     */
    @TableField("source_bill_number")
    private String sourceBillNumber;

    /**
     * 申请日期（付款申请发起日期）
     */
    @TableField("apply_date")
    private LocalDate applyDate;

    /**
     * 付款组织.编码（本笔付款申请的实际付款组织的guid。）
     */
    @TableField("pay_org_number")
    private String payOrgNumber;

    /**
     * 申请组织.编码（本笔付款的申请公司的guid，可以不是资金组织。）
     */
    @TableField("apply_org_number")
    private String applyOrgNumber;

    /**
     * 申请人.工号（本笔付款的申请人的guid，默认自动填充制单人。
     */
    @TableField("creator_number")
    private String creatorNumber;

    /**
     * 付款类型.编码（本笔付款申请的付款类型。）
     */
    @TableField("payment_type_number")
    private String paymentTypeNumber;

    /**
     * 付款标识.编码（异构系统：主动付款）
     */
    @TableField("payment_identify_number")
    private String paymentIdentifyNumber;

    /**
     * 付款状态 A:未付款, B:付款中, C:已付款, D:已排款（接口默认：未付款）
     */
    @TableField("paid_status")
    private String paidStatus;

    /**
     * 收付款总额（金额与分录明细的合计总额一致。）
     */
    @TableField("payee_amount")
    private BigDecimal payeeAmount;

    /**
     * 是否开发费用
     */
    @TableField("is_dev_fee")
    private Integer isDevFee;

    /**
     * 申请事由
     */
    @TableField("new_apply_cause")
    private String newApplyCause;

    /**
     * url
     */
    @TableField("url")
    private String url;

    /**
     * 收款单据编号
     */
    @TableField("text_field")
    private String textField;

    /**
     * 特殊场景 1:退款, 2:结转
     */
    @TableField("combo_field")
    private String comboField;

    /**
     * 是否已挂账为冲 0否1是
     */
    @TableField("is_strike_balance")
    private Integer isStrikeBalance;

    /**
     * 收款账号
     */
    @TableField("payee_bank_num")
    private String payeeBankNum;

    /**
     * 收款人姓名
     */
    @TableField("payee_name")
    private String payeeName;

    /**
     * 收款银行名称
     */
    @TableField("payee_bank_name")
    private String payeeBankName;

    /**
     * 付款金额
     */
    @TableField("act_pay_amt")
    private BigDecimal actPayAmt;

    /**
     * 付款金额折本位币
     */
    @TableField("local_amt")
    private BigDecimal localAmt;

    /**
     * 汇率
     */
    @TableField("exchange")
    private String exchange;

    /**
     * 货币类别 货币代码
     */
    @TableField("currency_number")
    private String currencyNumber;

    /**
     * 货币类别 货币名称
     */
    @TableField("currency_name")
    private String currencyName;

    /**
     * 结算方式 编码
     */
    @TableField("settle_type_number")
    private String settleTypeNumber;

    /**
     * 结算方式 名称
     */
    @TableField("settle_type_name")
    private String settleTypeName;

    /**
     * 摘要
     */
    @TableField("description")
    private String description;

    /**
     * 审核人 工号
     */
    @TableField("auditor_number")
    private String auditorNumber;

    /**
     * 审核人 姓名
     */
    @TableField("auditor_name")
    private String auditorName;

    /**
     * 创建人 姓名
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 修改人 工号
     */
    @TableField("modifier_number")
    private String modifierNumber;

    /**
     * 修改人 姓名
     */
    @TableField("modifier_name")
    private String modifierName;

    /**
     * 付款账号.账户编码
     */
    @TableField("payer_acct_bank_number")
    private String payerAcctBankNumber;

    /**
     * 付款账号 账户名称
     */
    @TableField("payer_acct_bank_name")
    private String payerAcctBankName;

    /**
     * 付款银行.编码
     */
    @TableField("payer_bank_number")
    private String payerBankNumber;

    /**
     * 付款银行 名称
     */
    @TableField("payer_bank_name")
    private String payerBankName;

    /**
     * 是否整单删除
     */
    @TableField("complete_del")
    private Integer completeDel;

    /**
     * 存入类型 [fail:付款失败, back:退款]
     */
    @TableField("back_type")
    private String backType;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 收款单号
     */
    @TableField("back_col_no")
    private String backColNo;

    /**
     * 修改时间
     */
    @TableField("modifity_time")
    private LocalDateTime modifityTime;

    /**
     * 是否已更新 [1:是, 0:否]
     */
    @TableField("if_update")
    private Integer ifUpdate;

    /**
     * 是否已经推送至金蝶系统
     */
    @TableField("is_push")
    private Integer isPush;

    /**
     * 对应金蝶接口参数类为 PayApplyEntry 字段 cas_payapplyentry
     */
    @TableField(exist = false)
    private List<CosmicPayApplyInfoEntity> infoItems = Lists.newArrayList();

    /**
     * 对应金蝶接口参数类为 PayApplyBusinessEntry 字段 cas_businessentry
     */
    @TableField(exist = false)
    private List<CosmicPayApplyBusinessEntity> businessItems = Lists.newArrayList();


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
