package cn.uone.bean.entity.tpi.cosmic.collection;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CollectionRewriteVo implements Serializable {

    private static final long serialVersionID = 1L;


    /**
     * 收款金额
     */
    @JSONField(name = "actrecamt")
    private BigDecimal actRecAmt;

    /**
     * 单据状态
     */
    @JSONField(name = "billstatus")
    private String billStatus;


    /**
     * 来源单据编码
     */
    @JSONField(name = "sourcenumber")
    private String sourceBillNumber;

    /**
     * 凭据单号
     */
    @JSONField(name = "vouchernumber")
    private String voucherNumber;

    /**
     * 单据编号
     */
    @JSONField(name = "billno")
    private String billNumber;


    /**
     * 特殊场景类型 01收款退回 02结转收款
     */
    @JSONField(name = "xmgd_tscj")
    private String specialType;

    /**
     * 特殊场景关联编号
     */
    @JSONField(name = "xmgd_tscjglbh")
    private String specialBillNumber;

    /**
     * 特殊场景付款申请单号
     */
    @JSONField(name = "xmgd_fkddh")
    private String specialPrepayNumber;

}
