package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_appointment")
public class ContAppointmentEntity extends BaseModel<ContAppointmentEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    @TableField(exist = false)
    private String projectName;



    /**
     * 意向客户名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 意向客户联系方式
     */
    @TableField("customer_tel")
    private String customerTel;

    /**
     * 预约看房时间
     */
    @TableField("app_date")
    private Date appDate;


    @Override
    public Serializable pkVal() {
        return id;
    }


}
