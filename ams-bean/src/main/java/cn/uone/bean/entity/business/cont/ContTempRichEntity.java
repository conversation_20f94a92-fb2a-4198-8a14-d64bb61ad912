package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 合同模板富文本配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_temp_rich")
public class ContTempRichEntity extends BaseModel<ContTempRichEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 合同模板ID
     */
    @TableField("temp_id")
    private String tempId;

    /**
     * 引言/封面
     */
    @TableField("foreword")
    private String foreword;

    /**
     * 前置内容【或协议内容】
     */
    @TableField("pre_content")
    private String preContent;

    /**
     * 通用条款
     */
    @TableField("general")
    private String general;

    /**
     * 附件
     */
    @TableField("annex")
    private String annex;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
