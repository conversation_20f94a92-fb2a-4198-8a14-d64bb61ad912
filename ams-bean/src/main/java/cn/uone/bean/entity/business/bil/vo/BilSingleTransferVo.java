package cn.uone.bean.entity.business.bil.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BilSingleTransferVo {

//    private String TRAN_TYPE;//交易类型
//    private String PAY_ACCNO;//转出账号
//    private String RECV_ACCNO;//转入账号
//    private String RECV_ACC_NAME;//转入账户名称
//    private String RECV_UBANKNO;//转入账户联行号
//    private String RECV_OPENACC_DEPT;//转入账户开户机构名称
//    private BigDecimal AMOUNT;//金额
//    private String CUR_TYPE;//01
//    private String USEOF;//用途
//    private String CST_PAY_NO;//客户方流水号

    private String PAY_ACCNO;//转出账号
    private String RECV_ACCNO;//转入账号
    private String RECV_ACC_NAME;//转入账户名称
    private String RECV_OPENACC_DEPT;//转入账户开户机构名称
    private String RECV_UBANKNO;//转入账户联行号
    private BigDecimal AMOUNT;//金额
    private String CUR_TYPE;//01
    private String USEOF;//用途
    private String CST_PAY_NO;//客户方流水号
    private String REM1;//备注


}
