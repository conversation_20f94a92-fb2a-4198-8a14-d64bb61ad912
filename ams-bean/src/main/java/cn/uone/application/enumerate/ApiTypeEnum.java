package cn.uone.application.enumerate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ApiTypeEnum {
    PROJECT("project","项目配置"),
    SMS_ALI("smsAli", "阿里云短信"),
    ICBC_PAY("icbcPay", "工行支付"),
    UNION_PAY("unionPay", "银联支付"),
    UNION_H5_PAY("unionH5Pay", "银联H5支付"),
    BAIWANG("baiwang", "百旺开票"),
    TQ_DIANBIAO("tqDianbiao", "拓强电表"),
    YK_WATER("ykWater", "无忧水表"),
    XMGJJ("xmgjj", "厦门公积金"),
    GZXYDOORLOCK("gzxyDoorLock", "广州翔羽门锁"),
    TRUDIAN("trudian","触点出入口门禁"),
    TRUDIAN_DIRECT("trudian_direct", "触点出入口门禁-直连"),
    MP_WEIXIN("mpWeixin","微信小程序"),
    CONTRACT_RECORD("contractRecord","合同备案"),
    DCFIRM("dcfirm","兴业银行银企直连"),
    COSMIC("cosmic","轨道金蝶系统");

    private String value;
    private String name;
    private static List<Map<String, Object>> list = new ArrayList<>();

    ApiTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ApiTypeEnum[] enums = ApiTypeEnum.values();
        for (ApiTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ApiTypeEnum enumerate : ApiTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }
}
