package cn.uone.application.constant;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@Slf4j
public class TransferConfig {

    public final static String HOST = "**************";
    //public final static String HOST = "127.0.0.1";
    public final static int PORT = 55555;

    public final static String CUST_ID = "*******************";// 客户号
    public final static String USER_ID = "WLPT01";// 操作员号
    public final static String PASSWORD = "********";// 密码

    public final static String PAYER_ACCOUNT = "35150198140100000265";// 付款方客户账号
    public final static String PAYER_NAME = "厦门多米克信息技术有限公司";// 付款方账户名称
    /**
     * 批量转账
     */
    public final static String TRAN_6W8040 = "6W8040";
    /**
     * 单笔转账
     */
    public final static String TRAN_6W8020 = "6W8020";
    /**
     * 批量转账流水查询
     */
    public final static String TRAN_6W0800 = "6W0800";
    /**
     * 账号、户名是否匹配校验交易
     */
    public final static String TRAN_6W1101 = "6W1101";
    /**
     * 公钥
     */
    public final static String CBBPAY_PUBLIC_KEY = "30819c300d06092a864886f70d010101050003818a00308186028180708670d315e2cf9c967f34fe888f3464d11b58732760b0b261e3b63dfa372d6aa028eccec9a154728f991b53f8e916bbd1d9d20e34bbb3fc4ec5f1512d2512a40943b5b6c37a7f940be38546006f5c422c83db25c9e7712f3cd15fd6d5ce793153321d8c547538e36a0b8f7a70afa5938ed2226786e091a408b5bac76a86ff99020111";
    /**
     * 测试 银联md5
     */

    public final static String miniUrl = "https://qr.chinaums.com/netpay-route-server/api/";

    public final static String h5Url = "https://qr-test2.chinaums.com/netpay-portal/webpay/pay.do";

    public final static String wxUrl = "https://api-mop.chinaums.com/v1/netpay/wx/unified-order";


    // test
//    public final static String MD5KEY = "fcAmtnx7MwismjWNhNKdHC44mNXtnEQeJkRrhKJwyrW2ysRR";
//    public final static String C2BURL = "https://qr-test2.chinaums.com/netpay-route-server/api/";
//    public final static String msgSrc = "WWW.TEST.COM";
//    public final static String msgId = "3194";
//    public final static String mid = "898201612345678";
//    public final static String tid = "88880001";
//    public final static String instMid = "YUEDANDEFAULT";
    // pro
    public final static String WXAPPID = "wx9bbdaa4812b5a613";
    public final static String WXMD5KEY = "mf8ctzrkKKW4Nc6eaj4YnRKSdBfH6BTb";
    public final static String APPID = "8a81c1bf7f93c86b01806e5bacaf090b";
    public final static String APPKEY = "bcab5fed05fb42b598c5075a70040451";
    public static String MD5KEY;
    public static String C2BURL;
    public static String msgSrc;
    public static String msgId;
    public static String mid;
    public static String tid;
    public static String instMid;
    public static String msgType;

    public static String sshHost;
    public static int sshPort;
    public static String sshUser;
    public static String sshPass;

    // modify by linderen on 20210829 start
    public static String notifyUrl;
    public static String notifyUrlCz;
    public static String returnUrl;
    // modify by linderen end
    public static String wxNotifyUrl;
    public static String wxNotifyUrlCz;

//    public final static String MD5KEY = "JW4MyzrkCZXEnfjKQ4Y5YCeiisEPbTQ8mTD7fPSJkHPaMDPc";
//    public final static String miniUrl = "https://qr.chinaums.com/netpay-route-server/api/";
//    public final static String h5Url = "https://qr.chinaums.com/netpay-portal/webpay/pay.do";
//    public final static String msgSrc = "WWW.XMSZZXXX.COM";
//    public final static String msgId = "4546";
//    public final static String notifyUrl = "http://www.uone.cn/xy-business/common/unionPay/unionPayNotify";
    public final static String query_url = "https://qr.chinaums.com/netpay-route-server/api/";
    private final static int[] BANKIN = {3510, 3515, ********, ********, ********, ********, ********, ********, ********};


    public final static String MD5KEY_H5 = "fcAmtnx7MwismjWNhNKdHC44mNXtnEQeJkRrhKJwyrW2ysRR";
    public final static String url_H5 = "https://qr-test2.chinaums.com/netpay-portal/webpay/pay.do";
    public final static String msgSrc_H5 = "WWW.TEST.COM";
    public final static String msgId_H5 = "3194";
    public final static String notifyUrl_H5 = "http://www.uone.cn/pms-business/common/unionPay/webUnionPayNotify";

    @Value("${unionPay.md5key}")
    public void setMD5KEY(String MD5KEY) {
        log.info("MD5KEY",MD5KEY);
        TransferConfig.MD5KEY = MD5KEY;
    }

    @Value("${unionPay.c2bUrl}")
    public void setC2BURL(String c2BURL) {
        C2BURL = c2BURL;
    }

    @Value("${unionPay.msgSrc}")
    public void setMsgSrc(String msgSrc) {
        log.info("msgSrc",msgSrc);
        TransferConfig.msgSrc = msgSrc;
    }

    @Value("${unionPay.msgId}")
    public void setMsgId(String msgId) {
        TransferConfig.msgId = msgId;
    }

    @Value("${unionPay.mid}")
    public void setMid(String mid) {
        TransferConfig.mid = mid;
    }

    @Value("${unionPay.tid}")
    public void setTid(String tid) {
        TransferConfig.tid = tid;
    }

    @Value("${unionPay.instMid}")
    public void setInstMid(String instMid) {
        TransferConfig.instMid = instMid;
    }

    @Value("${unionPay.msgType}")
    public void setMsgType(String msgType) {
        TransferConfig.msgType = msgType;
    }

    @Value("${unionPay.sshHost}")
    public void setSshHost(String sshHost) {
        TransferConfig.sshHost = sshHost;
    }

    @Value("${unionPay.sshPort}")
    public void setSshPort(int sshPort) {
        TransferConfig.sshPort = sshPort;
    }

    @Value("${unionPay.sshUser}")
    public void setSshUser(String sshUser) {
        TransferConfig.sshUser = sshUser;
    }

    @Value("${unionPay.sshPass}")
    public void setSshPass(String sshPass) {
        TransferConfig.sshPass = sshPass;
    }

    @Value("${unionPay.notifyUrl}")
    public void setNotifyUrl(String notifyUrl) {
        TransferConfig.notifyUrl = notifyUrl;
    }

    @Value("${unionPay.notifyUrlCz}")
    public void setNotifyUrlCz(String notifyUrlCz) {
        TransferConfig.notifyUrlCz = notifyUrlCz;
    }

    @Value("${unionPay.returnUrl}")
    public void setReturnUrl(String returnUrl) {
        TransferConfig.returnUrl = returnUrl;
    }

    @Value("${unionPay.wxNotifyUrl}")
    public void setWxNotifyUrl(String wxNotifyUrl) {
        TransferConfig.wxNotifyUrl = wxNotifyUrl;
    }

    @Value("${unionPay.wxNotifyUrlCz}")
    public void setWxNotifyUrlCz(String wxNotifyUrlCz) {
        TransferConfig.wxNotifyUrlCz = wxNotifyUrlCz;
    }
}
