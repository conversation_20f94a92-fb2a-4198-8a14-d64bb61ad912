package cn.uone.application.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName TempMsgVo
 * @Description 微信公众号-模板消息
 * <AUTHOR>
 * @Date 2021/5/25
 * @Version 1.0
 */
@Data
public class TempMsgVo {
    @ApiModelProperty(value="消息标题")
    private String title;
    @ApiModelProperty(value="项目名称")
    private String projectName;
    @ApiModelProperty(value="消息类型")
    private String msgType;
    @ApiModelProperty(value="消息内容")
    private String content;
    @ApiModelProperty(value="备注")
    private String remark;
    @ApiModelProperty(value="跳转链接")
    private String linkUrl;
}
