package cn.uone.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.onlineBid.BidAfficheEntity;
import cn.uone.bean.entity.business.onlineBid.WinAfficheEntity;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.util.TagsChecker;
import com.google.common.collect.Maps;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import lombok.extern.slf4j.Slf4j;
import org.beetl.core.GroupTemplate;
import org.beetl.core.Template;
import org.beetl.core.resource.StringTemplateResourceLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.*;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: ljl
 * @Date: 2018/12/26 19:06
 * @Description:
 */

@Component
@RefreshScope
@Slf4j
public class PdfUtil {

    @Autowired
    MinioUtil minioUtil;

    public String closeImgTags(String html) {
        // 正则表达式匹配<img标签但不包含闭合标签
        Pattern pattern = Pattern.compile("<img[^>]+", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(html);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String imgTag = matcher.group();
            // 如果img标签没有正确闭合，则添加闭合标签
            if (!imgTag.toLowerCase().contains("</img>")) {
                matcher.appendReplacement(sb, imgTag + "</img>");
            } else {
                matcher.appendReplacement(sb, imgTag);
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public byte[] getByte(String htmlStr, Map<String, Object> map) throws IOException, DocumentException {
        // 无效标签处理
        htmlStr = htmlStr.replaceAll("<br>", "<br/>");
        //<%%>  对于这种标签有问题, 标签修复会出现问题
        htmlStr = htmlStr.replaceAll("<%","&lt;%");
        htmlStr = htmlStr.replaceAll("%>","%&gt;");
        long start = System.currentTimeMillis();
        if(!TagsChecker.check(htmlStr)){
            //不是闭合标签, 则进行修复
            htmlStr = TagsChecker.fix(htmlStr);
        }
        long end = System.currentTimeMillis();
        System.out.println("html检测与修复花费时间:" + (end - start));
        htmlStr = htmlStr.replaceAll("&lt;%","<%");
        htmlStr = htmlStr.replaceAll("%&gt;","%>");

        //================================================================
        // 从map中获取页面大小设置，默认为A4
        Rectangle pageSize = PageSize.A4;
        String pageSizeStr = map.get("pageSize") == null ? "A4" : map.get("pageSize").toString();
        if ("A5".equalsIgnoreCase(pageSizeStr)) {
            pageSize = PageSize.A5;
        } else if ("A3".equalsIgnoreCase(pageSizeStr)) {
            pageSize = PageSize.A3;
        } else if ("A4".equalsIgnoreCase(pageSizeStr)) {
            pageSize = PageSize.A4;
        }
        Document document = new Document(pageSize);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PdfWriter writer = PdfWriter.getInstance(document, os);

        PdfDictionary uri = new PdfDictionary(PdfName.URI);
        uri.put(new PdfName("Base"), new PdfString("http://itextpdf.com/"));
        writer.getExtraCatalog().put(PdfName.URI, uri);

        String watermarkImage = map.get("watermarkImage")==null?"":map.get("watermarkImage").toString();
        // 加载水印图片
        Image image = null;
        if(StrUtil.isNotBlank(watermarkImage)){
            try {
                image = Image.getInstance(new URL(watermarkImage));
            } catch (BadElementException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        // 设置页脚
        Rectangle rect = new Rectangle(36, 54, 559, 788);
        rect.setBorderColor(BaseColor.BLACK);
        writer.setBoxSize("art", rect);
        BaseFont baseFont = BaseFont.createFont();
        HeaderFooter header = new HeaderFooter(image);
        writer.setPageEvent(header);
        document.open();


        //System.out.println(htmlStr);
        //XMLWorkerHelper.getInstance().parseXHtml(writer, document, new StringReader(renderer(htmlStr, map)));
        XMLWorkerHelper.getInstance().parseXHtml(writer, document, new ByteArrayInputStream(renderer(htmlStr, map).getBytes("UTF-8")),null, Charset.forName("UTF-8"),new FontProviderUtil());

        document.close();
        writer.close();

        int startPage = map.get("startPage")==null?1: (int) map.get("startPage");
        byte[] fileBuff = addPageNum(os.toByteArray(),startPage);

        if(os != null){
            os.flush();
            os.close();
        }
        return fileBuff;
    }

    /**
     * 添加页码
     * @param pdfBytes
     * @return
     */
    public byte[] addPageNum(byte[] pdfBytes,int startPage) throws IOException, DocumentException {
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            PdfReader reader =new PdfReader(pdfBytes);
            PdfStamper stamper = new PdfStamper(reader, os);
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
            Font font = new Font(baseFont, 10, Font.NORMAL, BaseColor.BLACK);

            int totalPages =reader.getNumberOfPages();
            for(int i=startPage;i<= totalPages;i++) {
                PdfContentByte under = stamper.getOverContent(i);
                Rectangle pageSize = reader.getPageSize(i);
                Paragraph paragraph = new Paragraph();
                paragraph.setFont(font);
                String text = String.format("第 %d 页,共 %d 页", i-startPage+1,totalPages-startPage+1);
                paragraph.add(text);
                float x = pageSize.getWidth()/2;
                float y = 18;
                ColumnText.showTextAligned(under,Element.ALIGN_CENTER, new Phrase(text,font),x, y,0);
                /*under.beginText();
                under.setFontAndSize(baseFont, font.getSize());
                under.showTextAligned(Element.ALIGN_CENTER, String.format("Page %d", i), x, y, 0);
                under.endText();*/
            }
            stamper.close();

            byte[] fileBuff = os.toByteArray();

            if(os != null){
                os.flush();
                os.close();
            }
            reader.close();
            return fileBuff;
        } catch (IOException | DocumentException e) {
            e.printStackTrace();
            return null;
        }
    }

    /*public static String pdf(String htmlStr, Map<String, Object> map, String pdfName) throws IOException, DocumentException {
        String ext = pdfName.substring(pdfName.lastIndexOf(".") + 1);
        FastDFSVo vo = new FastDFSVo(pdfName, getByte(htmlStr,map), ext);
        return FileUtil.upload(vo);
    }*/

    public String pdf(String htmlStr, Map<String, Object> map, String pdfName) throws IOException, DocumentException {
        return minioUtil.save(getByte(htmlStr,map),"pdf","application/pdf");
    }

    public String renderer(String htmlStr, Map<String, Object> map) {
        htmlStr = htmlStr.replace("&lt;","<").replace("&gt;",">");
        StringTemplateResourceLoader resourceLoader = new StringTemplateResourceLoader();
        GroupTemplate groupTemplate = null;
        Template template;

        try {
            org.beetl.core.Configuration cfg = org.beetl.core.Configuration.defaultConfiguration();
            groupTemplate = new GroupTemplate(resourceLoader, cfg);
            template = groupTemplate.getTemplate(htmlStr);
            template.binding(map);

            return template.render();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (groupTemplate != null) {
                groupTemplate.close();
            }
        }

        return null;
    }

    class HeaderFooter extends PdfPageEventHelper {
        Image watermarkImage;

        HeaderFooter(){
        }

        HeaderFooter(Image watermarkImage){
            this.watermarkImage = watermarkImage;
        }
        public void onEndPage(PdfWriter writer, Document document) {
            /*if(writer.getPageNumber() != 1){
                Rectangle rect = writer.getBoxSize("art");
                String text = "- " + (writer.getPageNumber()-1) + " -" ;
                float x = (rect.getLeft() + rect.getRight()) / 2;
                float y = rect.getBottom() - 36;
                ColumnText.showTextAligned(writer.getDirectContent(),
                        Element.ALIGN_CENTER, new Phrase(text),
                        x, y, 0);
            }*/

            // 加载水印图片
            if(watermarkImage != null){
                //Image watermarkImage = Image.getInstance(new URL("https://democs.domeke.com/static/yngy_watermarkImage.jpg"));
                PdfContentByte pdfContentByte = null;
                //设置水印位置和大小
                float pageWidth = writer.getPageSize().getWidth();
                float pageHeight = writer.getPageSize().getHeight();
                watermarkImage.scaleToFit(watermarkImage.getWidth()/2,watermarkImage.getHeight()/2);
                float imgH = watermarkImage.getScaledHeight();
                float imgW = watermarkImage.getScaledWidth();

                pdfContentByte = writer.getDirectContentUnder();
                // 计算每页可以放置图片的行数和列数
                int rows = (int) (pageHeight/imgH) +1; // 每页rows行
                int cols = (int) (pageWidth/imgW) +1; // 每页cols列
                float startX = 10; // 图片起始横坐标
                float startY = pageHeight - 110; // 图片起始纵坐标
                float xSpacing = (pageWidth - cols * imgW) / (cols - 1) + cols*20; // 图片横向间隔
                float ySpacing = (pageHeight - rows * imgH) / (rows - 1) + rows*6; // 图片纵向间隔

                // 添加多个图片
                for (int row = 0; row < rows; row++) {
                    for (int col = 0; col < cols; col++) {
                        float x = startX + col * (imgW + xSpacing);
                        float y = startY - row * (imgH + ySpacing);
                        try {
                            pdfContentByte.addImage(watermarkImage, imgW, 0, 0, imgH, x, y, true);
                        } catch (DocumentException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
    }
}
