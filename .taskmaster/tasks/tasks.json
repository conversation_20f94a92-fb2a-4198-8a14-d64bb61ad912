{"master": {"tasks": [{"id": 1, "title": "分析现有代码结构和数据流", "description": "分析CosmicPayApplyServiceImpl中getOrderItemsWithOrderTypeFilter方法的工作流程，理解BilOrderItemWithOrderVo对象的数据结构，包括address字段的格式、orderItemType的含义、时间字段的处理等，为备注格式化功能开发做准备", "status": "done", "dependencies": [], "priority": "high", "details": "1. 研究getOrderItemsWithOrderTypeFilter方法的实现逻辑\n2. 分析BilOrderItemWithOrderVo类的字段结构\n3. 理解address字段的数据格式和来源\n4. 研究OrderItemTypeEnum枚举的使用方式\n5. 分析时间字段startTime和endTime的格式", "testStrategy": "通过代码审查和调试，确保完全理解数据结构和业务流程", "subtasks": []}, {"id": 2, "title": "设计备注格式化工具类", "description": "设计一个专门的工具类来处理付款单明细备注的格式化，包括地址解析、费用类型转换、时间格式化等功能", "status": "done", "dependencies": [1], "priority": "high", "details": "1. 创建PaymentRemarkFormatter工具类\n2. 实现地址解析方法，从完整地址中提取项目名称和房间号\n3. 实现费用类型转换方法，使用OrderItemTypeEnum进行转换\n4. 实现时间格式化方法，统一使用yyyy-MM-dd格式\n5. 实现主要的备注格式化方法", "testStrategy": "编写单元测试验证各个方法的正确性", "subtasks": []}, {"id": 3, "title": "实现地址解析逻辑", "description": "实现从BilOrderItemWithOrderVo.bilOrderVo.address字段中解析出项目名称和房间号的逻辑，处理各种可能的地址格式", "status": "done", "dependencies": [2], "priority": "high", "details": "1. 分析现有地址数据的格式规律\n2. 设计正则表达式或字符串处理逻辑来提取项目名称\n3. 设计逻辑来提取房间号信息\n4. 处理异常情况和空值\n5. 确保解析结果的准确性", "testStrategy": "使用真实的地址数据进行测试，确保解析准确率", "subtasks": []}, {"id": 4, "title": "集成备注生成到付款申请流程", "description": "将备注格式化功能集成到CosmicPayApplyServiceImpl的付款申请处理流程中，确保在生成付款单明细时自动添加格式化的备注", "status": "done", "dependencies": [3], "priority": "high", "details": "1. 找到付款申请流程中设置备注的位置\n2. 调用备注格式化工具类生成备注\n3. 将生成的备注设置到相应的字段中\n4. 确保不影响现有的业务逻辑\n5. 处理异常情况，确保系统稳定性", "testStrategy": "在测试环境中验证付款申请流程，确保备注正确生成", "subtasks": []}, {"id": 5, "title": "编写单元测试", "description": "为备注格式化功能编写全面的单元测试，包括正常情况、边界情况和异常情况的测试", "status": "in-progress", "dependencies": [4], "priority": "medium", "details": "1. 为PaymentRemarkFormatter类编写单元测试\n2. 测试地址解析的各种情况\n3. 测试费用类型转换的正确性\n4. 测试时间格式化的准确性\n5. 测试异常情况的处理", "testStrategy": "确保测试覆盖率达到90%以上，所有测试用例通过", "subtasks": []}, {"id": 6, "title": "集成测试和验证", "description": "在测试环境中进行集成测试，验证备注格式化功能在实际业务流程中的正确性和稳定性", "status": "pending", "dependencies": [5], "priority": "medium", "details": "1. 在测试环境中部署代码\n2. 使用真实数据进行端到端测试\n3. 验证备注格式是否符合需求规范\n4. 检查是否影响现有功能\n5. 进行性能测试，确保无性能问题", "testStrategy": "通过实际业务场景验证功能的正确性和完整性", "subtasks": []}, {"id": 7, "title": "代码审查和优化", "description": "进行代码审查，优化代码质量，确保代码符合项目规范和最佳实践", "status": "pending", "dependencies": [6], "priority": "low", "details": "1. 进行代码审查，检查代码质量\n2. 优化代码结构和性能\n3. 确保代码符合项目编码规范\n4. 添加必要的注释和文档\n5. 处理代码审查中发现的问题", "testStrategy": "通过代码审查工具和人工审查确保代码质量", "subtasks": []}, {"id": 8, "title": "文档编写和部署准备", "description": "编写相关文档，包括功能说明、使用指南等，并准备生产环境部署", "status": "pending", "dependencies": [7], "priority": "low", "details": "1. 编写功能设计文档\n2. 编写用户使用指南\n3. 更新API文档\n4. 准备部署脚本和配置\n5. 制定上线计划", "testStrategy": "确保文档完整准确，部署流程经过验证", "subtasks": []}, {"id": 9, "title": "为合同列表接口增加终止时间范围筛选功能", "description": "为ZzctContContractController的list接口增加按合同终止时间（stopTime）进行范围筛选的功能，支持传入stopTimeStart和stopTimeEnd参数。", "details": "1. 定位到 `ZzctContContractController` 类及其 `getParaToMap` 方法。 2. 在 `getParaToMap` 方法中，添加对前端传入的 `stopTimeStart` 和 `stopTimeEnd` 参数的解析逻辑。 3. 确保将接收到的日期字符串安全地处理，并以 `yyyy-MM-dd` 格式放入传递给服务层的Map对象中。 4. 在对应的Service层和Mapper XML文件中，需要修改查询语句，增加对 `stopTime` 字段的范围查询条件。通常使用 `if` 标签判断参数是否存在，然后拼接 `AND stop_time >= #{stopTimeStart}` 和 `AND stop_time <= #{stopTimeEnd}` 等SQL片段。 5. 参考项目中其他时间范围筛选字段（如 `createTime` 或 `startTime`）的实现方式，以确保代码风格和实现逻辑的统一性。", "testStrategy": "1. 不带任何时间参数调用list接口，验证返回结果是否与之前一致，确保旧功能不受影响。 2. 只传入 `stopTimeStart` 参数，验证返回的所有合同数据的 `stopTime` 字段值均大于或等于该起始日期。 3. 只传入 `stopTimeEnd` 参数，验证返回的所有合同数据的 `stopTime` 字段值均小于或等于该截止日期。 4. 同时传入 `stopTimeStart` 和 `stopTimeEnd` 参数，验证返回结果是否精确落在该闭合时间区间内。 5. 传入一个预期不会有任何合同数据的日期范围，验证接口返回空列表。 6. 对比API返回的数据与直接在数据库中根据相同条件查询出的数据，确保数据一致性和准确性。", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "修改Controller层以接收并处理终止时间参数", "description": "在ZzctContContractController的getParaToMap方法中，增加对前端传入的stopTimeStart和stopTimeEnd参数的解析逻辑，并将其放入传递给服务层的Map中。", "dependencies": [], "details": "1. 定位到`ZzctContContractController.java`文件中的`getParaToMap`方法。\n2. 使用`getPara`或类似方法获取`stopTimeStart`和`stopTimeEnd`请求参数。\n3. 对获取到的参数进行非空校验。\n4. 将有效的日期字符串参数存入`map`对象，键名分别为`stopTimeStart`和`stopTimeEnd`。\n5. 参考该方法中对其他时间参数（如`startTime`）的处理逻辑，确保代码风格统一。\n<info added on 2025-06-24T02:38:01.299Z>\n实施的具体修改：\n1. 在ZzctContContractController.java的getParaToMap方法中添加了终止时间参数处理\n2. 添加的代码：\n   //终止时间\n   map.put(\"stopTimeStart\", !StrUtil.isBlank(request.getParameter(\"stopTimeStart\")) ? DateUtil.parse(request.getParameter(\"stopTimeStart\"), \"yyyy-MM-dd\") : null);\n   map.put(\"stopTimeEnd\", !StrUtil.isBlank(request.getParameter(\"stopTimeEnd\")) ? DateUtil.parse(request.getParameter(\"stopTimeEnd\"), \"yyyy-MM-dd\") : null);\n\n实现特点：\n- 完全按照现有代码风格实现，与其他时间范围参数（如checkoutDateStart/End）保持一致\n- 使用StrUtil.isBlank进行空值检查\n- 使用DateUtil.parse进行日期格式化，格式为\"yyyy-MM-dd\"\n- 将解析后的Date对象放入map中传递给服务层\n\n下一步：\n需要验证Service层能正确传递这些参数，然后在Mapper XML中添加对应的SQL查询条件。\n</info added on 2025-06-24T02:38:01.299Z>", "status": "done", "testStrategy": "使用API测试工具（如Postman）调用合同列表接口，传入`stopTimeStart`和`stopTimeEnd`参数。通过断点调试，验证在`getParaToMap`方法执行后，返回的Map对象中是否正确包含了这两个键值对。"}, {"id": 2, "title": "确保Service层正确传递查询参数", "description": "确认ZzctContContractService及其实现类能够接收包含终止时间范围的查询参数Map，并将其无误地传递给Mapper层进行数据库查询。", "dependencies": [], "details": "1. 检查`ZzctContContractServiceImpl`中调用列表查询的Mapper方法处。\n2. 由于参数是通过Map传递的，通常无需修改Service层的方法签名。\n3. 主要任务是验证从Controller层接收到的包含`stopTimeStart`和`stopTimeEnd`的Map对象被完整地传递给了`zzctContContractMapper`的对应查询方法。\n<info added on 2025-06-24T02:40:23.656Z>\n已确认Service层实现正确传递查询参数。在`ContContractServiceImpl.java`的`selectContContract`方法（约367-374行）中，调用`baseMapper.queryContractList`时，从Controller接收到的包含`stopTimeStart`和`stopTimeEnd`的Map参数被完整地传递给了Mapper。验证确认Service层的参数传递机制符合预期，无需修改代码。\n</info added on 2025-06-24T02:40:23.656Z>", "status": "done", "testStrategy": "编写一个针对Service层的单元测试，模拟传入一个包含`stopTimeStart`和`stopTimeEnd`的Map。使用Mockito等工具验证对应的Mapper方法是否被以正确的参数调用。"}, {"id": 3, "title": "修改Mapper XML文件以增加SQL范围查询条件", "description": "在ZzctContContractMapper.xml文件中，为合同列表的查询语句动态添加基于stop_time字段的范围查询逻辑。", "dependencies": [], "details": "1. 打开`ZzctContContractMapper.xml`文件，找到用于查询合同列表的`<select>`语句。\n2. 在`WHERE`子句中，使用MyBatis的`<if>`标签为`stopTimeStart`和`stopTimeEnd`添加动态SQL片段。\n3. `stopTimeStart`的条件为：`<if test=\"stopTimeStart != null and stopTimeStart != ''\"> AND stop_time >= #{stopTimeStart} </if>`。\n4. `stopTimeEnd`的条件为：`<if test=\"stopTimeEnd != null and stopTimeEnd != ''\"> AND stop_time &lt;= #{stopTimeEnd} </if>`。\n5. 确保字段名`stop_time`与数据库表结构一致。\n<info added on 2025-06-24T02:42:01.743Z>\n在`ams-central/ams-business/src/main/resources/mapper/cont/ContContractEntity.xml`文件的`queryContractList`方法中，已添加终止时间范围的查询条件。\n具体添加的SQL片段如下：\n<if test=\"map.stopTimeStart != null\">\n    AND DATE_FORMAT(A.stop_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.stopTimeStart}, '%Y-%m-%d')\n</if>\n<if test=\"map.stopTimeEnd != null\">\n    AND DATE_FORMAT(A.stop_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.stopTimeEnd}, '%Y-%m-%d')\n</if>\n实现要点：\n- 使用DATE_FORMAT函数确保日期比较的准确性。\n- 使用MyBatis的<if>标签实现动态SQL，仅当参数不为null时添加条件。\n- 使用&gt;=和&lt;=实现闭区间范围查询。\n- 已验证字段A.stop_time与数据库表结构一致，且stop_time字段已在Base_Column_List中正确包含。\n</info added on 2025-06-24T02:42:01.743Z>", "status": "done", "testStrategy": "启动应用后，通过接口调用触发查询。开启MyBatis的SQL日志输出，观察当传入时间参数时，控制台打印的SQL语句是否正确拼接了`AND stop_time >= ?`和`AND stop_time <= ?`条件。同时验证不传参数时，SQL语句保持原样。"}, {"id": 4, "title": "进行接口端到端测试并更新API文档", "description": "在后端代码全部修改完成后，对接口进行全面的功能测试，覆盖所有参数组合场景，并更新API文档（如Swagger）以包含新增的筛选参数。", "dependencies": [], "details": "1. 使用API测试工具，构造多种测试用例：\n   - 不传入任何时间参数。\n   - 只传入`stopTimeStart`。\n   - 只传入`stopTimeEnd`。\n   - 同时传入`stopTimeStart`和`stopTimeEnd`。\n   - 传入无效的日期格式。\n2. 验证每种情况下接口的返回数据是否符合预期。\n3. 如果项目使用Swagger，在`ZzctContContractController`的`list`方法上添加`@ApiImplicitParam`注解，说明`stopTimeStart`和`stopTimeEnd`参数的用途、数据类型和格式。", "status": "pending", "testStrategy": "执行一套完整的Postman或JMeter测试集，断言不同查询条件下的返回结果数量和内容是否正确。访问Swagger UI页面，检查API文档是否已更新，并且能够通过UI界面成功发起带新参数的测试请求。"}]}], "metadata": {"created": "2025-06-23T03:54:27.860Z", "updated": "2025-06-24T02:42:07.073Z", "description": "付款单明细备注格式化功能开发"}}}