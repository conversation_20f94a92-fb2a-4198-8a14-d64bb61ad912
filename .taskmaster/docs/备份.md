# 产品需求文档：Java应用安全漏洞修复项目

## 项目概述
本项目旨在修复rygym Java应用中发现的24个安全漏洞，其中包括1个危急漏洞、17个高危漏洞、5个中危漏洞和1个低危漏洞。这些漏洞涉及多个核心组件，包括Apache Log4j、Spring Framework、Apache Shiro、FastJSON、SnakeYAML等。

## 项目背景
通过安全扫描发现系统存在多个严重安全漏洞，这些漏洞可能导致：
- 远程代码执行
- 身份认证绕过
- 反序列化攻击
- 路径遍历攻击
- 未授权访问
- 拒绝服务攻击

## 技术栈
- **应用框架**: Spring Boot 2.2.5.RELEASE
- **安全框架**: Apache Shiro 1.4.0
- **日志组件**: Apache Log4j 2.12.1
- **JSON处理**: FastJSON 1.2.75
- **YAML处理**: SnakeYAML 1.33
- **数据库**: MySQL (mysql-connector-java 8.0.19)
- **构建工具**: Maven
- **应用类型**: 多模块Maven项目 (ams-central, ams-business, ams-crm, ams-tpi, ams-job)

## 漏洞清单

### 危急漏洞
1. **Apache Log4j 远程代码执行漏洞** (CVE-2021-44228、CVE-2021-45046)
   - 当前版本: 2.12.1
   - 目标版本: 2.17.1+
   - 影响: 远程代码执行

### 高危漏洞
2. **Spring WebMVC 路径遍历漏洞** (CVE-2024-38819)
   - 当前版本: 5.2.4.RELEASE
   - 目标版本: 5.3.41+
   
3. **Spring Boot 安全绕过漏洞** (CVE-2023-20873)
   - 当前版本: 2.2.5.RELEASE
   - 目标版本: 2.5.15+
   
4. **SnakeYAML 反序列化漏洞** (CVE-2022-1471)
   - 当前版本: 1.33
   - 目标版本: 2.0+
   
5. **Apache Shiro 多个身份认证绕过漏洞**
   - 当前版本: 1.4.0
   - 目标版本: 1.12.0+
   - 涉及CVE: CVE-2023-34478, CVE-2022-40664, CVE-2021-41303, CVE-2020-17510等
   
6. **FastJSON 反序列化漏洞** (CVE-2022-25845)
   - 当前版本: 1.2.75
   - 目标版本: 1.2.83+
   
7. **MySQL JDBC XXE漏洞** (CVE-2021-2471)
   - 当前版本: 8.0.19
   - 目标版本: 8.0.27+
   
8. **Apache Commons Collections 反序列化漏洞**
   - 当前版本: 3.2.1
   - 目标版本: 3.2.2+

### 中低危漏洞
- Spring Boot 拒绝服务漏洞 (CVE-2023-20883)
- Apache Log4j 其他相关漏洞
- Shiro 其他认证漏洞
- Spring Framework 反射型文件下载漏洞

## 核心需求

### 功能需求
1. **漏洞修复**
   - 升级所有存在漏洞的依赖组件到安全版本
   - 确保应用功能不受影响
   - 维持API兼容性

2. **依赖管理**
   - 统一管理Maven依赖版本
   - 解决版本冲突问题
   - 建立依赖安全检查机制

3. **安全配置**
   - 配置Shiro安全策略
   - 禁用不安全的序列化功能
   - 配置安全的日志记录

4. **测试验证**
   - 回归测试确保功能正常
   - 安全测试验证漏洞已修复
   - 性能测试确保无性能回退

### 非功能需求
1. **兼容性**: 保持与现有业务逻辑的兼容性
2. **性能**: 升级后性能不能下降
3. **稳定性**: 系统稳定性要求99.9%+
4. **安全性**: 修复所有已知高危漏洞

## 项目范围

### 包含模块
- ams-central (服务中心)
- ams-business (业务模块)
- ams-crm (客户关系管理)
- ams-tpi (第三方接口)
- ams-job (定时任务)
- ams-bean (公共组件)

### 主要任务
1. 依赖版本升级规划
2. 代码兼容性调整
3. 配置文件更新
4. 测试用例编写
5. 部署脚本更新
6. 安全配置优化
7. 文档更新

## 技术约束
- Java 8+ 兼容性
- Maven 3.6+ 构建
- 现有数据库架构不变
- API接口向后兼容
- 最小化业务逻辑变更

## 验收标准
1. 所有高危和危急漏洞修复完成
2. 应用正常启动和运行
3. 核心业务功能测试通过
4. 安全扫描通过(无高危漏洞)
5. 性能测试满足现有指标
6. 部署流程验证通过

## 风险评估
- **高风险**: 版本升级导致的兼容性问题
- **中风险**: 配置变更引起的功能异常
- **低风险**: 性能轻微下降

## 项目时间线
- 总工期: 2-3周
- 第一阶段(1周): 依赖升级和基础修复
- 第二阶段(1周): 配置优化和测试
- 第三阶段(0.5-1周): 部署验证和文档更新 

# 基于依赖分析的精准漏洞修复计划

## 第一部分：现状深度诊断 (根据 dependency:tree 分析)

您的依赖树提供了宝贵的线索。在制定行动计划前，我们必须先准确理解当前战场的真实情况。

### 1.1. 关键发现

| 组件                  | 依赖树中的发现                                       | 风险评估与解读                                                                                                                              |
| --------------------- | ---------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| **Spring Boot**         | `spring-boot:jar:2.2.5.RELEASE`                        | **核心风险**。您当前使用的是 Spring Boot 2.2.x，该版本已于2021年5月停止开源支持。这意味着您不仅错过了大量安全修复，其管理的传递性依赖（如Tomcat, Netty）也已严重过时，这是您列表中多个Spring相关漏洞的直接原因。 |
| **Spring Cloud**        | `spring-cloud-starter-openfeign:jar:2.2.2.RELEASE`     | 与 Spring Boot 2.2.x 对应的 Hoxton 版本。同样已停止支持，必须随 Spring Boot 一同升级。                                                        |
| **Spring Cloud Alibaba**| `spring-cloud-starter-alibaba-nacos-*:jar:2.2.5.RELEASE` | 版本与 Spring Boot 2.2.x 匹配，升级 Spring Boot 时必须同步升级此依赖以保持兼容性。                                                              |
| **Apache Log4j 2**      | `log4j-core:jar:2.19.0`                                | **已修复**。恭喜！您已经手动将 Log4j 2 升级到了一个非常安全的版本（2.19.0），这表明 Log4Shell (CVE-2021-44228) 等相关漏洞在您的主日志框架中已得到解决。 |
| **Apache Log4j 1.x**    | `log4j:log4j:jar:1.2.14`                               | **新发现的风险**。此版本由 `net.sourceforge.jexcelapi:jxl` 间接引入。Log4j 1.x 本身存在多个安全漏洞（非Log4Shell），且早已停止维护，应从依赖中彻底移除。   |
| **Apache Shiro**        | `shiro-core:jar:1.3.2`, `shiro-spring:jar:1.4.0`, `shiro-core:jar:1.13.0` | **版本冲突与混乱**。您的不同模块中存在三个不同版本的Shiro。虽然部分模块已升级到安全的 1.13.0，但 1.3.2 和 1.4.0 版本的存在意味着您的系统依然暴露在所有未修复的Shiro漏洞之下。必须强制统一版本。 |
| **FastJSON**            | `fastjson:jar:1.2.47`, `fastjson:jar:2.0.25`           | **版本冲突与危急风险**。您的 `ams-bean` 模块引入了极其危险的 1.2.47 版本，它受到 CVE-2022-25845 反序列化漏洞的影响。同时，其他模块又引入了安全的2.x版本。这种混乱状态极易导致在运行时意外使用到脆弱版本。 |
| **SnakeYAML**           | `snakeyaml:jar:1.33`                                   | **已修复**。您已手动将 SnakeYAML 升级到 1.33，此版本不受 CVE-2022-1471 影响。                                                                 |
| **MySQL Connector/J**   | `mysql-connector-java:jar:8.0.19`, `mysql-connector-java:jar:8.0.27` | **版本冲突与风险**。`ams-business` 模块使用的 8.0.19 版本受 CVE-2021-2471 XXE漏洞影响。`ams-crm` 模块使用的 8.0.27 是修复此漏洞的最低版本。必须统一到更高的安全版本。 |
| **Commons Collections** | `commons-collections:commons-collections:jar:3.2.1`    | **高危传递性依赖**。此版本由古老的 `net.sf.json-lib:json-lib` 引入，存在经典的反序列化RCE漏洞。这是您列表中 Apache Commons Collections 漏洞的直接来源。 |

### 1.2. 核心问题总结

-   **缺乏统一的依赖管理**：多模块项目中，依赖版本散落在各个子模块的 `pom.xml` 中，导致了严重版本冲突和安全风险。
-   **核心框架版本过时**：Spring Boot 2.2.x 是所有Spring相关漏洞和大量过时传递性依赖的根源。
-   **遗留高风险库**：项目中仍在使用如 `net.sf.json-lib`、`log4j:log4j` 1.x 等早已被淘汰且存在已知漏洞的库。

---

## 第二部分：新版三阶段修复行动计划

此计划将聚焦于利用Maven的 `<dependencyManagement>` 机制，在父POM中集中、强制地统一所有依赖版本，从根源上解决问题。

### 第一阶段：紧急止血与版本统一 (预计1-3天) - **重点实施阶段**

**核心策略**: 不动核心框架，仅通过在父POM中进行版本管理，快速消除最危急的RCE和认证绕过漏洞。**先在CRM应用进行验证，确保项目可以运行，然后升级为统一依赖管理**。

#### 1.1 验证策略

| 步骤 | 行动 | 验证目标 | 成功标准 |
|------|------|----------|----------|
| 1 | **CRM应用单独验证** | 验证单个模块修复可行性 | `ams-crm` 模块正常启动和基本功能运行 |
| 2 | **父POM统一管理** | 将验证成功的版本推广到全项目 | 所有模块使用统一的安全版本 |
| 3 | **整体构建验证** | 确保整个项目可以正常构建运行 | `mvn clean install` 成功，关键模块启动正常 |

#### 1.2 具体实施任务

| 优先级 | 任务名称 | 涉及漏洞 | 验证方法 | 精确行动指令 |
|--------|----------|----------|----------|--------------|
| **P0 - 最高** | **1. CRM模块Shiro版本验证统一** | 所有Shiro漏洞 | 单独编译启动ams-crm | 1. 在 `ams-crm/pom.xml` 中强制指定 `shiro.version=1.13.0`<br>2. 验证CRM应用正常启动<br>3. 测试登录认证功能 |
| **P0 - 最高** | **2. CRM模块FastJSON版本安全化** | CVE-2022-25845 | CRM模块功能测试 | 1. 在 `ams-crm/pom.xml` 中移除旧版FastJSON<br>2. 统一使用Jackson或安全版本FastJSON<br>3. 验证JSON序列化功能正常 |
| **P1 - 紧急** | **3. CRM模块MySQL驱动升级** | CVE-2021-2471 | 数据库连接测试 | 1. 在 `ams-crm/pom.xml` 中升级到 `mysql-connector-j:8.4.0`<br>2. 验证数据库连接和基本CRUD操作 |
| **P1 - 紧急** | **4. 父POM统一依赖管理升级** | 版本冲突问题 | 全项目构建测试 | 1. 在根 `pom.xml` 的 `<dependencyManagement>` 中统一管理所有安全版本<br>2. 强制所有子模块继承安全版本<br>3. 验证 `mvn dependency:tree` 无版本冲突 |
| **P2 - 中等** | **5. 危险传递依赖排除** | Commons Collections/Log4j 1.x | 依赖树检查 | 1. 排除 `json-lib` 中的 `commons-collections`<br>2. 排除 `jxl` 中的 `log4j:log4j`<br>3. 确认危险依赖完全移除 |

#### 1.3 CRM应用验证具体步骤

**步骤1: CRM单独验证**
```bash
# 1. 修改ams-crm/pom.xml中的关键依赖版本
# 2. 单独构建CRM模块
cd ams-central/ams-crm
mvn clean compile

# 3. 验证依赖无冲突
mvn dependency:tree | grep -E "(shiro|fastjson|mysql)"

# 4. 启动CRM应用验证
mvn spring-boot:run
```

**步骤2: 父POM统一管理**
```xml
<!-- 根pom.xml中添加 -->
<properties>
    <shiro.version>1.13.0</shiro.version>
    <mysql.version>8.4.0</mysql.version>
    <fastjson1.version>1.2.83</fastjson1.version>
</properties>

<dependencyManagement>
    <dependencies>
        <!-- 统一Shiro版本 -->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-web</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        
        <!-- 统一MySQL版本 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        
        <!-- 统一FastJSON版本(临时) -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson1.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

**步骤3: 整体验证**
```bash
# 全项目构建验证
mvn clean install

# 依赖冲突检查
mvn dependency:tree | grep -E "CONFLICT|LATEST"

# 安全扫描验证
mvn org.owasp:dependency-check-maven:10.0.3:check -Dformat=XML
```

#### 1.4 第一阶段成功标准

- ✅ CRM应用使用安全版本依赖并正常启动
- ✅ 所有高危Shiro、FastJSON、MySQL漏洞修复
- ✅ 项目整体构建成功，无依赖冲突
- ✅ 关键业务功能验证通过
- ✅ 依赖安全扫描通过（高危漏洞清零）

---

### 第二阶段：核心框架现代化升级 (预计1-2周)

此阶段是解决系统性风险的根本性举措。我们将把 Spring Boot 升级到 2.7.x 系列，这是 Java 8 环境下最理想的长期支持版本。

| 优先级 | 任务名称                                 | 涉及漏洞           | 负责人 | 精确行动指令 (修改父 pom.xml)                                                                                                                              |
| ------ | ------------------------------------------ | ------------------ | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------- |
| P0 - 核心 | 6. 升级 Spring Boot、Spring Cloud 及 Alibaba Cloud | 所有Spring相关漏洞 | 开发   | **这是中心任务！** 必须严格按照版本对应关系进行升级，以避免兼容性灾难。在父POM中执行以下操作：<br>1. 修改 `<parent>`：将 Spring Boot 版本升级到 `2.7.18`。<br>2. 引入BOMs：在 `<dependencyManagement>` 中，按顺序导入 `spring-cloud-dependencies` 和 `spring-cloud-alibaba-dependencies` 的BOM（Bill of Materials），并指定兼容的版本。 |

#### 父POM修改示例 (第二阶段):

```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version>
    <relativePath/>
</parent>

<properties>
    <java.version>8</java.version>
    <spring-cloud.version>2021.0.9</spring-cloud.version>
    <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    <shiro.version>1.13.0</shiro.version>
    <mysql.version>8.4.0</mysql.version>
</properties>

<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-dependencies</artifactId>
            <version>${spring-cloud.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-dependencies</artifactId>
            <version>${spring-cloud-alibaba.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

**重要提示：** 完成此阶段后，必须进行全面的功能回归测试。框架的大版本升级可能会引入行为变更。

---

### 第三阶段：战略性清理与加固 (持续进行)

完成核心升级后，我们需要清理历史遗留问题，并建立长效机制。

| 优先级 | 任务名称                     | 涉及漏洞/问题         | 负责人   | 精确行动指令                                                                                                                                      |
| ------ | ------------------------------ | ----------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| P1 - 核心 | 7. 迁移 FastJSON 至 Jackson  | FastJSON历史安全风险    | 开发     | 您的项目中已存在 Jackson。此任务的目标是彻底移除所有对 `com.alibaba:fastjson` 的依赖。请参考上一份报告中提供的"FastJSON到Jackson的迁移手册"，系统性地替换代码和注解。 |
| P2 - 中等 | 8. 替换遗留库                | commons-collections 漏洞根源 | 开发     | 1. **替换 `net.sf.json-lib`**：这是一个非常古老的库，应使用 Jackson (`ObjectMapper`) 替代其功能。<br>2. **替换 `net.sourceforge.jexcelapi:jxl`**：这是一个早已停止维护的Excel操作库，推荐使用现代的替代品，如 `org.apache.poi` (您的项目中已存在) 或 `com.alibaba:easyexcel`。 |
| P2 - 中等 | 9. 清理子模块POM依赖         | 依赖管理混乱            | 开发     | 在所有子模块（`ams-bean`, `ams-business`等）的 `pom.xml` 中，删除所有已在父POM `<dependencyManagement>` 中管理的依赖的 `<version>` 标签。这能确保所有模块都从父POM继承统一、正确的版本。 |
| P0 - 运维 | 10. 升级并加固 Redis         | CVE-2024-46981, 未授权访问 | 运维/DBA | 此任务不变：升级Redis服务器到安全版本（如 7.2.5+），并确保已配置强密码、绑定内网IP访问。                                                              |

### 1.4. 最终版本推荐与兼容性矩阵 (Java 8 环境)

| 框架                  | 要求版本/序列          | BOM Artifact ID                      | 推荐BOM版本    | 依据/来源                                             |
| --------------------- | -------------------- | ------------------------------------ | -------------- | ----------------------------------------------------- |
| Spring Boot           | 2.7.x                | `spring-boot-dependencies`           | 2.7.18         | 您的 `dependency:tree` 显示当前为2.2.x，2.7.x是Java 8下最理想的LTS版本。 |
| Spring Cloud          | 2021.0.x (Jubilee)   | `spring-cloud-dependencies`          | 2021.0.9 (或最新) | Spring官方版本对应关系。                                |
| Spring Cloud Alibaba  | 2021.x               | `spring-cloud-alibaba-dependencies`  | 2021.0.5.0     | Alibaba官方版本对应关系。                               |

---

## 第三部分：实施保障与验证机制

### 3.1 质量保障流程

**每个阶段完成后必须执行的验证清单:**

1. **构建验证**: `mvn clean install -U`
2. **依赖冲突检查**: `mvn dependency:tree | grep -E "CONFLICT|LATEST"`  
3. **安全扫描**: `mvn org.owasp:dependency-check-maven:10.0.3:check`
4. **功能测试**: 关键业务流程手工验证
5. **性能基准测试**: 确保无性能回退

### 3.2 回滚策略

每个阶段开始前：
- 创建Git分支: `git checkout -b security-fix-stage-{N}`
- 记录当前依赖状态: `mvn dependency:tree > dependencies_before_stage_{N}.txt`
- 如有问题，立即回滚到上一个稳定状态

### 3.3 风险控制

**高风险缓解措施:**
- 第一阶段重点验证CRM模块，确保单模块可行性
- 分步推进，每步验证成功后再进行下一步
- 保持原有Spring Boot框架版本作为第一阶段的基础
- 建立详细的测试用例和验证标准

这个经过重新思考的计划，将直接解决您 `dependency:tree` 中暴露出的每一个具体问题。请严格按照此顺序，特别是优先完成第一阶段的"CRM验证+版本统一"工作，它将为您后续的核心升级打下坚实的基础。 